@use '../../../../scss/globals.scss' as *;

/**
 * NewsCards Styles
 *
 * Touch-optimized news cards for Steam game updates and announcements.
 * Designed for seamless experience across desktop, tablet, and handheld devices.
 *
 * Key Features:
 * - Touch-friendly card interactions (48px+ minimum)
 * - Steam Deck optimizations (52px+ targets)
 * - Responsive carousel with smooth scrolling
 * - Enhanced visual hierarchy and readability
 * - Accessible keyboard navigation
 * - Smooth animations and transitions
 */

.news-cards-container {
  margin-bottom: calc($spacing-unit * 4);

  // Steam Deck optimizations
  @media (max-width: 1280px) and (max-height: 800px) {
    margin-bottom: calc($spacing-unit * 4.5);
  }

  @media (max-width: 768px) {
    margin-bottom: calc($spacing-unit * 3);
  }
}

.news-cards-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: calc($spacing-unit * 2);

  h2 {
    font-size: 1.75rem;
    font-weight: 700;
    color: $muted-color;
    margin: 0;
    background: linear-gradient(135deg, $muted-color 0%, rgba(255, 255, 255, 0.8) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      font-size: 2rem;
    }

    @media (max-width: 768px) {
      font-size: 1.5rem;
    }
  }
}

.news-cards-controls {
  display: flex;
  gap: calc($spacing-unit * 1);
}

// Navigation Buttons - Touch Optimized
.news-scroll-button {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 48px; // Touch target minimum
  min-height: 48px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%);
  color: $body-color;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(16px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  svg {
    width: 20px;
    height: 20px;
  }

  &:hover:not(.disabled) {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
    border-color: rgba(255, 255, 255, 0.15);
    color: $muted-color;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  &.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  &:active:not(.disabled) {
    transform: translateY(0);
  }

  // Steam Deck optimizations
  @media (max-width: 1280px) and (max-height: 800px) {
    min-width: 52px;
    min-height: 52px;
    border-radius: 14px;

    svg {
      width: 22px;
      height: 22px;
    }
  }

  // Touch feedback for mobile devices
  @media (hover: none) and (pointer: coarse) {
    &:active:not(.disabled) {
      transform: scale(0.95);
      transition: transform 0.1s ease;
    }
  }
}

.news-cards-scroll {
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.news-cards-list {
  display: flex;
  gap: calc($spacing-unit * 1.5);
  padding-bottom: calc($spacing-unit * 0.5);
}

// News Cards - Touch Optimized
.news-card {
  flex: 0 0 320px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  backdrop-filter: blur(16px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  min-height: 400px; // Ensure consistent card height

  &:hover {
    transform: translateY(-6px);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.2);
    border-color: rgba($brand-teal, 0.3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  }

  &.event-card {
    border-color: rgba($brand-teal, 0.4);
    box-shadow: 0 4px 16px rgba($brand-teal, 0.1);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, $brand-teal, #20c5a8);
      z-index: 1;
    }

    &:hover {
      border-color: rgba($brand-teal, 0.6);
      box-shadow: 0 12px 48px rgba($brand-teal, 0.2);
    }
  }

  // Steam Deck optimizations
  @media (max-width: 1280px) and (max-height: 800px) {
    flex: 0 0 360px;
    border-radius: 20px;
    min-height: 440px;
  }

  @media (max-width: 768px) {
    flex: 0 0 280px;
    border-radius: 14px;
    min-height: 360px;
  }

  // Touch feedback for mobile devices
  @media (hover: none) and (pointer: coarse) {
    &:active {
      transform: scale(0.98);
      transition: transform 0.1s ease;
    }
  }
}

.news-card-image {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .news-card:hover & img {
    transform: scale(1.05);
  }
}

.event-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba($brand-teal, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  backdrop-filter: blur(8px);

  svg {
    width: 12px;
    height: 12px;
  }
}

.external-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 50%;
  backdrop-filter: blur(8px);

  svg {
    width: 12px;
    height: 12px;
  }
}

.news-card-content {
  padding: calc($spacing-unit * 1.5);
  display: flex;
  flex-direction: column;
  gap: calc($spacing-unit * 0.75);
}

.news-card-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.75rem;
  color: $body-color;
}

.news-card-game {
  font-weight: 600;
  color: $brand-teal;
}

.news-card-date {
  opacity: 0.8;
}

.news-card-title {
  font-size: 1rem;
  font-weight: 600;
  color: $muted-color;
  margin: 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-card-summary {
  font-size: 0.875rem;
  color: $body-color;
  line-height: 1.4;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-card-footer {
  margin-top: auto;
  padding-top: calc($spacing-unit * 0.5);
  border-top: 1px solid rgba($border-color, 0.2);
}

.news-card-label {
  font-size: 0.75rem;
  color: $body-color;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

// Loading state
.news-cards-loading {
  display: flex;
  gap: calc($spacing-unit * 1.5);
  overflow: hidden;
  width: 100%;
}

.news-card-skeleton {
  flex: 1;
  min-width: 280px;
  max-width: 320px;
  height: 280px;
  background: linear-gradient(90deg,
    rgba($dark-background-color, 0.4) 25%,
    rgba($dark-background-color, 0.7) 50%,
    rgba($dark-background-color, 0.4) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 12px;
  position: relative;
  overflow: hidden;

  // Add more detailed skeleton structure
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 160px;
    background: linear-gradient(90deg,
      rgba($dark-background-color, 0.6) 25%,
      rgba($dark-background-color, 0.9) 50%,
      rgba($dark-background-color, 0.6) 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.8s infinite;
    border-radius: 12px 12px 0 0;
  }

  // Content area skeleton
  &::after {
    content: '';
    position: absolute;
    bottom: 24px;
    left: 24px;
    right: 24px;
    height: 80px;
    background: linear-gradient(
      180deg,
      rgba($dark-background-color, 0.3) 0%,
      rgba($dark-background-color, 0.5) 20%,
      transparent 25%,
      transparent 35%,
      rgba($dark-background-color, 0.4) 40%,
      rgba($dark-background-color, 0.6) 60%,
      transparent 65%,
      transparent 75%,
      rgba($dark-background-color, 0.3) 80%,
      rgba($dark-background-color, 0.5) 100%
    );
    border-radius: 6px;
    animation: shimmer 2.2s infinite;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Smooth transition when news loads
.news-cards-list {
  animation: fadeInUp 0.6s ease-out;
}

// Stagger animation for individual cards
.news-card {
  animation: fadeInUp 0.4s ease-out;
  animation-fill-mode: both;

  @for $i from 1 through 10 {
    &:nth-child(#{$i}) {
      animation-delay: #{($i - 1) * 0.1}s;
    }
  }
}

// Empty state
.news-cards-empty {
  text-align: center;
  padding: calc($spacing-unit * 3);
  color: $body-color;

  p {
    margin: 0;
    font-size: 0.875rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .news-card {
    flex: 0 0 280px;
  }
  
  .news-cards-header h2 {
    font-size: 1.25rem;
  }
  
  .news-cards-controls {
    display: none;
  }
}
