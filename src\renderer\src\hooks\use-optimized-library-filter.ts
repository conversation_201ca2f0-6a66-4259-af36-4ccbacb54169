import { useMemo, useCallback } from 'react';
import type { LibraryGame, LibraryFilters } from '@types';
import { filterGamesByGenres } from '@renderer/utils/genre-utils';
import { sortLibraryGames } from '@renderer/utils/library-sort-utils';

interface UseOptimizedLibraryFilterOptions {
  enableMemoization?: boolean;
  enableIndexing?: boolean;
  batchSize?: number;
}

interface FilterIndexes {
  titleIndex: Map<string, LibraryGame[]>;
  genreIndex: Map<string, LibraryGame[]>;
  installedIndex: LibraryGame[];
  notInstalledIndex: LibraryGame[];
}

/**
 * Optimized library filtering hook with indexing and memoization
 * Provides significant performance improvements for large libraries
 */
export function useOptimizedLibraryFilter(
  library: LibraryGame[],
  filters: LibraryFilters,
  selectedCollection?: string,
  collections?: any[],
  options: UseOptimizedLibraryFilterOptions = {}
) {
  const {
    enableMemoization = true,
    enableIndexing = true,
    batchSize = 1000,
  } = options;

  // Create search indexes for faster filtering
  const indexes = useMemo(() => {
    if (!enableIndexing || library.length < 100) {
      return null; // Skip indexing for small libraries
    }

    const titleIndex = new Map<string, LibraryGame[]>();
    const genreIndex = new Map<string, LibraryGame[]>();
    const installedIndex: LibraryGame[] = [];
    const notInstalledIndex: LibraryGame[] = [];

    library.forEach((game) => {
      // Title indexing - create n-grams for better search
      const title = game.title.toLowerCase();
      for (let i = 0; i < title.length - 1; i++) {
        const ngram = title.substring(i, i + 2);
        if (!titleIndex.has(ngram)) {
          titleIndex.set(ngram, []);
        }
        titleIndex.get(ngram)!.push(game);
      }

      // Genre indexing
      if (game.genres) {
        game.genres.forEach((genre) => {
          if (!genreIndex.has(genre)) {
            genreIndex.set(genre, []);
          }
          genreIndex.get(genre)!.push(game);
        });
      }

      // Installation status indexing
      if (game.executablePath) {
        installedIndex.push(game);
      } else {
        notInstalledIndex.push(game);
      }
    });

    return {
      titleIndex,
      genreIndex,
      installedIndex,
      notInstalledIndex,
    } as FilterIndexes;
  }, [library, enableIndexing]);

  // Optimized search function using indexes
  const searchGames = useCallback(
    (games: LibraryGame[], query: string): LibraryGame[] => {
      if (!query || query.length < 2) return games;

      if (indexes && query.length >= 2) {
        // Use indexed search for better performance
        const queryLower = query.toLowerCase();
        const matchingGames = new Set<LibraryGame>();

        // Search using n-grams
        for (let i = 0; i < queryLower.length - 1; i++) {
          const ngram = queryLower.substring(i, i + 2);
          const ngramMatches = indexes.titleIndex.get(ngram) || [];
          ngramMatches.forEach((game) => {
            if (game.title.toLowerCase().includes(queryLower)) {
              matchingGames.add(game);
            }
          });
        }

        return Array.from(matchingGames);
      }

      // Fallback to linear search
      return games.filter((game) =>
        game.title.toLowerCase().includes(query.toLowerCase())
      );
    },
    [indexes]
  );

  // Optimized genre filtering using indexes
  const filterByGenres = useCallback(
    (games: LibraryGame[], genres: string[]): LibraryGame[] => {
      if (genres.length === 0) return games;

      if (indexes) {
        // Use indexed search
        const matchingGames = new Set<LibraryGame>();
        genres.forEach((genre) => {
          const genreMatches = indexes.genreIndex.get(genre) || [];
          genreMatches.forEach((game) => matchingGames.add(game));
        });
        return Array.from(matchingGames);
      }

      // Fallback to utility function
      return filterGamesByGenres(games, genres);
    },
    [indexes]
  );

  // Optimized installation status filtering
  const filterByInstallationStatus = useCallback(
    (games: LibraryGame[], showInstalled: boolean, showNotInstalled: boolean): LibraryGame[] => {
      if (!showInstalled && !showNotInstalled) return games;

      if (indexes) {
        // Use indexed search
        if (showInstalled && !showNotInstalled) {
          return indexes.installedIndex.filter((game) => games.includes(game));
        }
        if (showNotInstalled && !showInstalled) {
          return indexes.notInstalledIndex.filter((game) => games.includes(game));
        }
      }

      // Fallback to linear filtering
      return games.filter((game) => {
        const isInstalled = Boolean(game.executablePath);
        if (showInstalled && showNotInstalled) return true;
        if (showInstalled) return isInstalled;
        if (showNotInstalled) return !isInstalled;
        return true;
      });
    },
    [indexes]
  );

  // Main filtering function with optimizations
  const filteredAndSortedGames = useMemo(() => {
    if (!enableMemoization) {
      // Skip memoization for testing or debugging
      return library;
    }

    let games = [...library];

    // Collection filtering first (most selective)
    if (selectedCollection && collections) {
      const collection = collections.find((c) => c.id === selectedCollection);
      if (collection?.gameIds) {
        const gameIdSet = new Set(collection.gameIds);
        games = games.filter((game) => gameIdSet.has(game.id));
      }
    }

    // Early return if no games after collection filtering
    if (games.length === 0) return games;

    // Apply filters in order of selectivity (most selective first)
    
    // 1. Installation status (often very selective)
    if (filters.showInstalledOnly || filters.showNotInstalledOnly) {
      games = filterByInstallationStatus(
        games,
        filters.showInstalledOnly,
        filters.showNotInstalledOnly
      );
    }

    // 2. Genre filtering (moderately selective)
    if (filters.genres.length > 0) {
      games = filterByGenres(games, filters.genres);
    }

    // 3. Search query (can be very selective)
    if (filters.searchQuery && typeof filters.searchQuery === 'string') {
      games = searchGames(games, filters.searchQuery);
    }

    // 4. Sorting (always last)
    games = sortLibraryGames(games, filters.sortBy);

    return games;
  }, [
    library,
    filters,
    selectedCollection,
    collections,
    enableMemoization,
    searchGames,
    filterByGenres,
    filterByInstallationStatus,
  ]);

  // Performance metrics
  const metrics = useMemo(() => {
    return {
      totalGames: library.length,
      filteredGames: filteredAndSortedGames.length,
      reductionRatio: library.length > 0 ? filteredAndSortedGames.length / library.length : 0,
      indexingEnabled: !!indexes,
      memoizationEnabled: enableMemoization,
    };
  }, [library.length, filteredAndSortedGames.length, indexes, enableMemoization]);

  return {
    filteredAndSortedGames,
    metrics,
    indexes: !!indexes,
  };
}
