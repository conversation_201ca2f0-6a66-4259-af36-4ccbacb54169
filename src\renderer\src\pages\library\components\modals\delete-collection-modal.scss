@use "../../../../scss/globals.scss";

.delete-collection-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: globals.$modal-z-index;
  animation: backdropFadeIn 0.3s ease-out;
}

.delete-collection-modal {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 20px;
  padding: 0;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.4s ease-out;

  &__header {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 4);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.02) 100%);
  }

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, rgba(128, 29, 30, 0.2) 0%, rgba(128, 29, 30, 0.1) 100%);
    border: 1px solid rgba(128, 29, 30, 0.3);
    border-radius: 12px;
    flex-shrink: 0;

    svg {
      color: globals.$danger-color;
    }
  }

  &__title {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
    color: globals.$muted-color;
    flex: 1;
  }

  &__close {
    background: none;
    border: none;
    color: globals.$body-color;
    cursor: pointer;
    padding: calc(globals.$spacing-unit * 1);
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: globals.$muted-color;
    }
  }

  &__content {
    padding: calc(globals.$spacing-unit * 4);
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 3);
  }

  &__collection-preview {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 3);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.04) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
  }

  &__collection-color {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  &__collection-info {
    flex: 1;
  }

  &__collection-name {
    margin: 0 0 calc(globals.$spacing-unit * 0.5) 0;
    font-size: 16px;
    font-weight: 600;
    color: globals.$muted-color;
  }

  &__collection-description {
    margin: 0;
    font-size: globals.$small-font-size;
    color: globals.$body-color;
    opacity: 0.8;
  }

  &__message {
    margin: 0;
    font-size: globals.$body-font-size;
    color: globals.$body-color;
    line-height: 1.5;
    text-align: center;
  }

  &__warning {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.5);
    padding: calc(globals.$spacing-unit * 2);
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    border: 1px solid rgba(255, 193, 7, 0.2);
    border-radius: 8px;
    font-size: globals.$small-font-size;
    color: globals.$warning-color;

    svg {
      flex-shrink: 0;
    }
  }

  &__footer {
    padding: calc(globals.$spacing-unit * 4);
    border-top: 1px solid rgba(255, 255, 255, 0.08);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
  }

  &__actions {
    display: flex;
    gap: calc(globals.$spacing-unit * 2);
    justify-content: flex-end;
  }
}

// Animations
@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Responsive design
@media (max-width: 768px) {
  .delete-collection-modal {
    width: 95%;
    margin: calc(globals.$spacing-unit * 2);

    &__header {
      padding: calc(globals.$spacing-unit * 3);
    }

    &__content {
      padding: calc(globals.$spacing-unit * 3);
    }

    &__footer {
      padding: calc(globals.$spacing-unit * 3);
    }

    &__actions {
      flex-direction: column-reverse;
    }
  }
}
