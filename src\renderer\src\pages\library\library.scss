@use "../../scss/globals.scss";

.library {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: globals.$background-color;
  padding: calc(globals.$spacing-unit * 4) calc(globals.$spacing-unit * 5);
  gap: 0; // Remove gap to let components handle their own spacing
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  max-width: 1600px;
  margin: 0 auto;

  // Enhanced visual hierarchy with progressive spacing
  > * + * {
    margin-top: calc(globals.$spacing-unit * 2.5);
  }

  // Special spacing for major sections
  .library-collection-header + .library-news {
    margin-top: calc(globals.$spacing-unit * 4);
  }

  .library-news + .library__content {
    margin-top: calc(globals.$spacing-unit * 4);
  }

  .library-navigation-bar + .library-collection-header {
    margin-top: calc(globals.$spacing-unit * 3);
  }

  // Steam Deck optimizations (1280x800)
  @media (max-width: 1280px) and (max-height: 800px) {
    padding: calc(globals.$spacing-unit * 3.5) calc(globals.$spacing-unit * 4);

    > * + * {
      margin-top: calc(globals.$spacing-unit * 2.25);
    }
  }

  @media (max-width: 1024px) {
    padding: calc(globals.$spacing-unit * 3) calc(globals.$spacing-unit * 3.5);

    > * + * {
      margin-top: calc(globals.$spacing-unit * 2);
    }
  }

  // Filtering overlay and loading indicator
  &__filtering-overlay {
    position: relative;
    opacity: 0.7;
    pointer-events: none;
    transition: opacity 0.2s ease;
  }

  &__filtering-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1);
    background: rgba(0, 0, 0, 0.8);
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    border-radius: 8px;
    color: globals.$muted-color;
    font-size: globals.$small-font-size;
    font-weight: 500;
    backdrop-filter: blur(8px);
    z-index: 10;
    pointer-events: none;

    span {
      white-space: nowrap;
    }
  }

  &__filtering-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid globals.$brand-teal;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @media (max-width: 768px) {
    padding: calc(globals.$spacing-unit * 2.5) calc(globals.$spacing-unit * 2.5);

    > * + * {
      margin-top: calc(globals.$spacing-unit * 1.75);
    }

    // Adjust special spacing for mobile
    .library-collection-header + .library-news {
      margin-top: calc(globals.$spacing-unit * 3);
    }

    .library-news + .library__content {
      margin-top: calc(globals.$spacing-unit * 3);
    }

    .library-navigation-bar + .library-collection-header {
      margin-top: calc(globals.$spacing-unit * 2);
    }
  }

  @media (max-width: 480px) {
    padding: calc(globals.$spacing-unit * 2);

    > * + * {
      margin-top: calc(globals.$spacing-unit * 1.5);
    }

    // Further reduce spacing for small screens
    .library-collection-header + .library-news {
      margin-top: calc(globals.$spacing-unit * 2.5);
    }

    .library-news + .library__content {
      margin-top: calc(globals.$spacing-unit * 2.5);
    }

    .library-navigation-bar + .library-collection-header {
      margin-top: calc(globals.$spacing-unit * 1.5);
    }
  }

  // Header Section - Similar to Home page
  &__header {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 2);
  }

  &__title-section {
    display: flex;
    align-items: baseline;
    gap: calc(globals.$spacing-unit * 2);

    h1 {
      margin: 0;
      color: globals.$muted-color;
      font-size: 32px;
      font-weight: 700;

      @media (max-width: 768px) {
        font-size: 28px;
      }
    }
  }

  &__game-count {
    color: globals.$body-color;
    font-size: globals.$body-font-size;
    font-weight: 500;
  }

  // Controls Section - Similar to Home page buttons
  &__controls {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 2);
    flex-wrap: wrap;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      gap: calc(globals.$spacing-unit * 1.5);
    }
  }

  &__search {
    flex: 1;
    min-width: 300px;

    @media (max-width: 768px) {
      min-width: 100%;
    }
  }

  &__control-buttons {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.5);
    flex-wrap: wrap;

    @media (max-width: 768px) {
      justify-content: space-between;
      width: 100%;
    }
  }

  &__view-controls {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: calc(globals.$spacing-unit * 0.25);
    gap: calc(globals.$spacing-unit * 0.25);
  }

  &__view-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: transparent;
    border: none;
    border-radius: 6px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      color: globals.$muted-color;
    }

    &--active {
      background: rgba(255, 255, 255, 0.12);
      color: globals.$muted-color;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }

  &__card-size-controls {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: calc(globals.$spacing-unit * 0.25);
    gap: calc(globals.$spacing-unit * 0.25);
  }

  &__card-size-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: transparent;
    border: none;
    border-radius: 6px;
    color: globals.$body-color;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      color: globals.$muted-color;
    }

    &--active {
      background: rgba(255, 255, 255, 0.12);
      color: globals.$muted-color;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    }
  }

  // Collections Section - New Design
  &__collections {
    border-bottom: 1px solid globals.$border-color;
    padding-bottom: calc(globals.$spacing-unit * 2);
  }

  &__collections-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: calc(globals.$spacing-unit * 2);
  }

  &__collections-toggle {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1);
    background: none;
    border: none;
    color: globals.$muted-color;
    cursor: pointer;
    padding: calc(globals.$spacing-unit * 0.5) 0;
    transition: all 0.2s ease;

    &:hover {
      color: globals.$brand-teal;
      transform: translateX(2px);
    }

    h3 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
    }

    svg {
      transition: transform 0.2s ease;
    }
  }

  &__collections-count {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: calc(globals.$spacing-unit * 0.25) calc(globals.$spacing-unit * 0.75);
    font-size: globals.$small-font-size;
    font-weight: 600;
    color: globals.$body-color;
    margin-left: calc(globals.$spacing-unit * 1);
  }

  &__collections-content {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: calc(globals.$spacing-unit * 2);
    animation: slideDown 0.3s ease-out;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: calc(globals.$spacing-unit * 1.5);
    }
  }

  &__collection-card {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.5);
    padding: calc(globals.$spacing-unit * 2);
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid globals.$border-color;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;

    &:hover {
      background: rgba(255, 255, 255, 0.04);
      border-color: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    &--active {
      background: rgba(255, 255, 255, 0.06);
      border-color: globals.$brand-teal;
      box-shadow: 0 0 0 2px rgba(22, 177, 149, 0.2);

      .library__collection-card-icon {
        background: globals.$brand-teal !important;
        color: white;
      }
    }
  }

  &__collection-card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    color: globals.$muted-color;
    flex-shrink: 0;
    transition: all 0.2s ease;
  }

  &__collection-card-content {
    flex: 1;
    min-width: 0;

    h4 {
      margin: 0 0 calc(globals.$spacing-unit * 0.5) 0;
      color: globals.$muted-color;
      font-size: 16px;
      font-weight: 600;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    span {
      color: globals.$body-color;
      font-size: globals.$small-font-size;
      font-weight: 500;
    }

    p {
      margin: calc(globals.$spacing-unit * 0.5) 0 0 0;
      color: globals.$body-color;
      font-size: globals.$small-font-size;
      opacity: 0.8;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &__collection-card-actions {
    display: flex;
    gap: calc(globals.$spacing-unit * 0.5);
    opacity: 0;
    transition: opacity 0.2s ease;

    .library__collection-card:hover & {
      opacity: 1;
    }

    button {
      padding: calc(globals.$spacing-unit * 0.5);
      min-width: auto;
      width: 32px;
      height: 32px;
    }
  }



  // Mobile Quick Filters - Less prominent
  &__mobile-quick-filters {
    @media (min-width: 1025px) {
      display: none; // Hide on desktop since we have filters in nav bar
    }

    @media (max-width: 1024px) {
      margin-bottom: calc(globals.$spacing-unit * 1.5);

      .library-quick-filters__content {
        justify-content: center;
      }

      .library-quick-filters__chip {
        font-size: 10px;
        padding: calc(globals.$spacing-unit * 0.5) calc(globals.$spacing-unit * 0.75);
        opacity: 0.8;
      }
    }
  }

  // Content Section - Enhanced layout and spacing
  &__content {
    flex: 1;
    min-height: 0;
    margin-top: calc(globals.$spacing-unit * 3);
    position: relative;

    // Better visual separation from header
    &::before {
      content: '';
      position: absolute;
      top: calc(globals.$spacing-unit * -1.5);
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.1) 20%,
        rgba(255, 255, 255, 0.1) 80%,
        transparent 100%);
    }

    @media (max-width: 768px) {
      margin-top: calc(globals.$spacing-unit * 2);
    }
  }

  // Error State
  &__error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: calc(globals.$spacing-unit * 8);
    text-align: center;
    gap: calc(globals.$spacing-unit * 2);

    h2 {
      color: globals.$muted-color;
      font-size: 24px;
      font-weight: 600;
      margin: 0;
    }

    p {
      color: globals.$body-color;
      font-size: globals.$body-font-size;
      margin: 0;
    }
  }
}

// Animations
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 500px;
  }
}

@keyframes filtersSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
