@use "../../../scss/globals.scss";

/**
 * CollectionModal Styles
 *
 * Touch-optimized modal for creating and editing game collections.
 * Designed for seamless experience across desktop, tablet, and handheld devices.
 *
 * Key Features:
 * - Touch-friendly form controls (48px+ minimum)
 * - Steam Deck optimizations (52px+ targets)
 * - Responsive modal sizing and positioning
 * - Accessible keyboard navigation
 * - Smooth animations and transitions
 * - Progressive disclosure for advanced options
 */

.collection-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(12px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: backdropFadeIn 0.3s ease-out;
  padding: calc(globals.$spacing-unit * 2);
  box-sizing: border-box;

  // Steam Deck optimizations
  @media (max-width: 1280px) and (max-height: 800px) {
    padding: calc(globals.$spacing-unit * 1.5);
  }

  @media (max-width: 768px) {
    padding: calc(globals.$spacing-unit * 1);
  }
}

.collection-modal {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 20px;
  width: 100%;
  max-width: 560px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(40px);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: calc(globals.$spacing-unit * 4) calc(globals.$spacing-unit * 4) calc(globals.$spacing-unit * 3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  }

  &__title {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    color: globals.$muted-color;
    background: linear-gradient(135deg, globals.$muted-color 0%, rgba(255, 255, 255, 0.8) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: -0.01em;
  }

  // Close Button - Touch Optimized
  &__close {
    background: transparent;
    border: none;
    color: globals.$body-color;
    cursor: pointer;
    padding: calc(globals.$spacing-unit * 1.25);
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 48px; // Touch target minimum
    min-height: 48px;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: globals.$muted-color;
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-width: 52px;
      min-height: 52px;
      padding: calc(globals.$spacing-unit * 1.5);
      border-radius: 14px;
    }

    // Touch feedback for mobile devices
    @media (hover: none) and (pointer: coarse) {
      &:active {
        transform: scale(0.9);
        transition: transform 0.1s ease;
      }
    }
  }

  &__content {
    flex: 1;
    padding: calc(globals.$spacing-unit * 4);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 4);

    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 3px;
      transition: background 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  &__field {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 0.75);
    position: relative;
  }

  &__label {
    font-size: globals.$body-font-size;
    font-weight: 600;
    color: globals.$muted-color;
    margin-bottom: calc(globals.$spacing-unit * 0.5);
  }

  &__error {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.5);
    color: #ff6b6b;
    font-size: globals.$small-font-size;
    font-weight: 500;
    margin-top: calc(globals.$spacing-unit * 0.25);

    &--general {
      background: rgba(255, 107, 107, 0.1);
      border: 1px solid rgba(255, 107, 107, 0.2);
      border-radius: 8px;
      padding: calc(globals.$spacing-unit * 0.75) globals.$spacing-unit;
      margin-bottom: calc(globals.$spacing-unit * 2);
    }

    svg {
      flex-shrink: 0;
    }
  }

  &__field-hint {
    font-size: globals.$small-font-size;
    color: globals.$body-color;
    opacity: 0.7;
    margin-top: calc(globals.$spacing-unit * 0.25);
    text-align: right;
  }

  &__textarea {
    background-color: globals.$background-color;
    border: 1px solid globals.$border-color;
    border-radius: 4px;
    padding: calc(globals.$spacing-unit * 1.5);
    color: globals.$muted-color;
    font-family: inherit;
    font-size: globals.$body-font-size;
    resize: vertical;
    min-height: 80px;
    transition: border-color ease 0.2s;

    &:focus {
      outline: none;
      border-color: rgba(255, 255, 255, 0.4);
    }

    &::placeholder {
      color: globals.$body-color;
    }
  }

  // Color Picker - Touch Optimized
  &__color-picker {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: calc(globals.$spacing-unit * 2);
    max-width: 480px;
    padding: calc(globals.$spacing-unit * 2);
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 16px;

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      grid-template-columns: repeat(5, 1fr);
      gap: calc(globals.$spacing-unit * 2.5);
      padding: calc(globals.$spacing-unit * 2.5);
      max-width: 520px;
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
      gap: calc(globals.$spacing-unit * 1.5);
      padding: calc(globals.$spacing-unit * 1.5);
    }
  }

  &__color-option {
    width: 52px;
    height: 52px;
    border-radius: 14px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 6px 24px rgba(0, 0, 0, 0.3);
    }

    &--selected {
      border-color: rgba(255, 255, 255, 0.8);
      transform: scale(1.05);
      box-shadow:
        0 0 0 3px rgba(255, 255, 255, 0.3),
        0 6px 24px rgba(0, 0, 0, 0.3);

      &:hover {
        transform: scale(1.1);
      }
    }

    &:active {
      transform: scale(0.95);
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      width: 56px;
      height: 56px;
      border-radius: 16px;
    }

    // Touch feedback for mobile devices
    @media (hover: none) and (pointer: coarse) {
      &:active {
        transform: scale(0.9);
        transition: transform 0.1s ease;
      }
    }

    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 16px;
      height: 16px;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='white'%3E%3Cpath d='M13.78 4.22a.75.75 0 010 1.06l-7.25 7.25a.75.75 0 01-1.06 0L2.22 9.28a.75.75 0 011.06-1.06L6 10.94l6.72-6.72a.75.75 0 011.06 0z'/%3E%3C/svg%3E");
      opacity: 0;
      transition: opacity ease 0.2s;
    }

    &--selected::after {
      opacity: 1;
    }
  }

  &__preview {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid globals.$border-color;
    border-radius: 6px;
    padding: calc(globals.$spacing-unit * 2);
  }

  &__preview-label {
    font-size: globals.$small-font-size;
    color: globals.$body-color;
    margin-bottom: globals.$spacing-unit;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
  }

  &__preview-item {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.5);
    padding: calc(globals.$spacing-unit * 1.5);
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
  }

  &__preview-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  &__preview-name {
    font-size: globals.$body-font-size;
    font-weight: 500;
    color: globals.$muted-color;
  }

  &__footer {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 3) calc(globals.$spacing-unit * 4);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: calc(globals.$spacing-unit * 2);
  }
}

// Enhanced animations
@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-40px) rotateX(10deg);
    filter: blur(4px);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02) translateY(-10px) rotateX(0deg);
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0) rotateX(0deg);
    filter: blur(0);
  }
}

// Stagger animation for form fields
.collection-modal__field {
  animation: fieldSlideIn 0.4s ease-out;
  animation-fill-mode: both;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
}

@keyframes fieldSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Color option animations
.collection-modal__color-option {
  animation: colorOptionPop 0.3s ease-out;
  animation-fill-mode: both;

  @for $i from 1 through 12 {
    &:nth-child(#{$i}) {
      animation-delay: #{0.05s * $i};
    }
  }
}

@keyframes colorOptionPop {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-180deg);
  }
  70% {
    transform: scale(1.1) rotate(10deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}
