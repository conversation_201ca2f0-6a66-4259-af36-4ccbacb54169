import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import {
  AppsIcon,
  StarIcon,
  ClockIcon,
  DownloadIcon,
  PlayIcon,
  FileDirectoryIcon,
} from "@primer/octicons-react";

import { useLibrary, useLibraryCollections } from "@renderer/hooks";
import "./library-collection-header.scss";

interface CollectionInfo {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}

export function LibraryCollectionHeader() {
  const { t } = useTranslation("library");
  const { library } = useLibrary();
  const { selectedCollection, collectionSource, getCollectionById, collections } = useLibraryCollections();

  // Get collection information
  const collectionInfo = useMemo((): CollectionInfo | null => {
    // If no collection is selected, always show "All Games" header
    if (!selectedCollection) {
      const installedCount = library.filter(game => Boolean(game.executablePath)).length;
      const favoriteCount = library.filter(game => game.isFavorite).length;

      let dynamicDescription = t("all_games_description");
      if (library.length > 0) {
        const parts = [];
        if (installedCount > 0) {
          parts.push(`${installedCount} ${installedCount === 1 ? t("installed").toLowerCase() : t("installed").toLowerCase()}`);
        }
        if (favoriteCount > 0) {
          parts.push(`${favoriteCount} ${favoriteCount === 1 ? t("favorite").toLowerCase() : t("favorites").toLowerCase()}`);
        }
        if (parts.length > 0) {
          dynamicDescription = `${t("all_games_description")} • ${parts.join(" • ")}`;
        }
      }

      return {
        id: "all-games",
        name: t("all_games"),
        description: dynamicDescription,
        icon: <AppsIcon size={24} />,
        color: "#6366f1",
      };
    }

    // First check if it's a user-created collection
    const userCollection = getCollectionById(selectedCollection);
    if (userCollection) {
      return {
        id: userCollection.id,
        name: userCollection.name,
        description: userCollection.description || t("custom_collection_description"),
        icon: <FileDirectoryIcon size={24} />,
        color: userCollection.color || "#6366f1",
      };
    }

    // Then check predefined collections
    const predefinedCollections: Record<string, Omit<CollectionInfo, "id">> = {
      favorites: {
        name: t("favorites"),
        description: t("favorites_description"),
        icon: <StarIcon size={24} />,
        color: "#f59e0b",
      },
      "recently-played": {
        name: t("recently_played"),
        description: t("recently_played_description"),
        icon: <ClockIcon size={24} />,
        color: "#10b981",
      },
      installed: {
        name: t("installed"),
        description: t("installed_description"),
        icon: <DownloadIcon size={24} />,
        color: "#1c9749",
      },
      "not-played": {
        name: t("not_played"),
        description: t("not_played_description"),
        icon: <PlayIcon size={24} />,
        color: "#dc2626",
      },
    };

    const predefinedCollection = predefinedCollections[selectedCollection];
    if (predefinedCollection) {
      return {
        id: selectedCollection,
        ...predefinedCollection,
      };
    }

    // Fallback for unknown collections
    return {
      id: selectedCollection,
      name: selectedCollection,
      description: t("custom_collection_description"),
      icon: <AppsIcon size={24} />,
      color: "#6366f1",
    };
  }, [selectedCollection, t, getCollectionById]);

  // Calculate game counts
  const gameCounts = useMemo(() => {
    // If no collection is selected, always show all games count
    if (!selectedCollection) {
      return {
        filtered: library.length,
        total: library.length,
      };
    }

    let filteredGames = library;

    switch (selectedCollection) {
      case "favorites":
        filteredGames = library.filter(game => game.isFavorite);
        break;
      case "recently-played":
        filteredGames = library.filter(game => 
          game.lastTimePlayed && 
          Date.now() - new Date(game.lastTimePlayed).getTime() < 7 * 24 * 60 * 60 * 1000
        );
        break;
      case "installed":
        filteredGames = library.filter(game => Boolean(game.executablePath));
        break;
      case "not-played":
        filteredGames = library.filter(game => !game.lastTimePlayed);
        break;
      default:
        // For custom collections, we'd need to implement collection filtering
        filteredGames = library;
        break;
    }

    return {
      filtered: filteredGames.length,
      total: library.length,
    };
  }, [selectedCollection, library]);

  if (!collectionInfo || !gameCounts) {
    return null;
  }

  // Determine if this is from insights and should show enhanced design
  const isFromInsights = collectionSource === "insights";
  // Determine if this is the default "All Games" view
  const isAllGamesView = !selectedCollection;

  return (
    <div className={`library-collection-header ${isFromInsights ? 'library-collection-header--from-insights' : ''} ${isAllGamesView ? 'library-collection-header--all-games' : ''}`}>
      <div className="library-collection-header__backdrop">
        <div className="library-collection-header__gradient" style={{
          background: `linear-gradient(135deg, ${collectionInfo.color}15 0%, ${collectionInfo.color}05 100%)`
        }}></div>
        {isFromInsights && (
          <div className="library-collection-header__insights-glow" style={{
            background: `radial-gradient(circle at center, ${collectionInfo.color}20 0%, transparent 70%)`
          }}></div>
        )}
      </div>

      <div className="library-collection-header__content">
        <div className="library-collection-header__main">
          <div className="library-collection-header__icon-wrapper">
            <div
              className="library-collection-header__icon"
              style={{
                backgroundColor: collectionInfo.color,
                boxShadow: `0 8px 32px ${collectionInfo.color}40, 0 4px 16px ${collectionInfo.color}20`
              }}
            >
              {collectionInfo.icon}
            </div>
            {isFromInsights && (
              <div className="library-collection-header__insights-ring"
                   style={{ borderColor: collectionInfo.color }}></div>
            )}
          </div>

          <div className="library-collection-header__text">
            <div className="library-collection-header__title-row">
              <h2 className="library-collection-header__title">
                {collectionInfo.name}
              </h2>
              {isFromInsights && (
                <div className="library-collection-header__insights-badge">
                  <span className="library-collection-header__insights-dot"></span>
                  <span className="library-collection-header__insights-text">
                    {t("from_insights")}
                  </span>
                </div>
              )}
            </div>
            <p className="library-collection-header__description">
              {collectionInfo.description}
            </p>
          </div>
        </div>

        <div className="library-collection-header__stats">
          <div className="library-collection-header__stat-item library-collection-header__stat-item--primary">
            <span className="library-collection-header__stat-number">{gameCounts.filtered}</span>
            <span className="library-collection-header__stat-label">
              {gameCounts.filtered === 1 ? t("game") : t("games")}
            </span>
          </div>
          {gameCounts.filtered !== gameCounts.total && (
            <div className="library-collection-header__stat-item library-collection-header__stat-item--secondary">
              <span className="library-collection-header__stat-number">{gameCounts.total}</span>
              <span className="library-collection-header__stat-label">{t("total")}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
