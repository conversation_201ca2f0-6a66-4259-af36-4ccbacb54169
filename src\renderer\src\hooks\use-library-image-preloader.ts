import { useEffect, useCallback, useRef, useState } from 'react';
import type { LibraryGame } from '@types';
import { useImageCache } from './use-image-cache';

interface UseLibraryImagePreloaderOptions {
  enabled?: boolean;
  priority?: 'visible' | 'recent' | 'alphabetical' | 'smart';
  batchSize?: number;
  delayBetweenBatches?: number;
  maxConcurrentLoads?: number;
  preloadOnIdle?: boolean;
  respectDataSaver?: boolean;
}

interface PreloadStats {
  totalImages: number;
  preloadedImages: number;
  failedImages: number;
  progress: number;
  isPreloading: boolean;
  estimatedTimeRemaining: number;
}

/**
 * Smart image preloader for library games with priority-based loading
 * Optimizes image loading based on user behavior and system resources
 */
export function useLibraryImagePreloader(
  games: LibraryGame[],
  options: UseLibraryImagePreloaderOptions = {}
): PreloadStats & {
  preloadImages: () => void;
  pausePreloading: () => void;
  resumePreloading: () => void;
  clearCache: () => void;
} {
  const {
    enabled = true,
    priority = 'smart',
    batchSize = 5,
    delayBetweenBatches = 100,
    maxConcurrentLoads = 3,
    preloadOnIdle = true,
    respectDataSaver = true,
  } = options;

  const { preloadImages: cachePreloadImages, clearCache } = useImageCache();
  const [stats, setStats] = useState<PreloadStats>({
    totalImages: 0,
    preloadedImages: 0,
    failedImages: 0,
    progress: 0,
    isPreloading: false,
    estimatedTimeRemaining: 0,
  });

  const abortController = useRef<AbortController>();
  const preloadQueue = useRef<string[]>([]);
  const preloadedUrls = useRef<Set<string>>(new Set());
  const failedUrls = useRef<Set<string>>(new Set());
  const isPaused = useRef(false);
  const startTime = useRef<number>(0);

  // Check if user has data saver enabled
  const isDataSaverEnabled = useCallback(() => {
    if (!respectDataSaver) return false;
    
    // Check for data saver preference
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      return connection?.saveData === true || connection?.effectiveType === 'slow-2g';
    }
    
    return false;
  }, [respectDataSaver]);

  // Generate image URLs for games
  const getImageUrls = useCallback((games: LibraryGame[]): string[] => {
    const urls: string[] = [];
    
    games.forEach(game => {
      // Primary cover image
      if (game.iconUrl) {
        urls.push(game.iconUrl);
      }
      
      // Steam library images if available
      if (game.shop === 'steam' && game.objectId) {
        const steamId = game.objectId;
        urls.push(
          `https://cdn.akamai.steamstatic.com/steam/apps/${steamId}/library_600x900.jpg`,
          `https://cdn.akamai.steamstatic.com/steam/apps/${steamId}/header.jpg`,
          `https://cdn.akamai.steamstatic.com/steam/apps/${steamId}/library_hero.jpg`
        );
      }
    });
    
    return urls.filter(Boolean);
  }, []);

  // Prioritize images based on strategy
  const prioritizeImages = useCallback((games: LibraryGame[]): LibraryGame[] => {
    switch (priority) {
      case 'recent':
        return [...games].sort((a, b) => {
          const aTime = a.lastTimePlayed ? new Date(a.lastTimePlayed).getTime() : 0;
          const bTime = b.lastTimePlayed ? new Date(b.lastTimePlayed).getTime() : 0;
          return bTime - aTime;
        });
      
      case 'alphabetical':
        return [...games].sort((a, b) => a.title.localeCompare(b.title));
      
      case 'smart':
        return [...games].sort((a, b) => {
          // Smart priority: favorites > recently played > installed > alphabetical
          const aScore = (a.isFavorite ? 1000 : 0) + 
                        (a.lastTimePlayed ? 500 : 0) + 
                        (a.executablePath ? 100 : 0);
          const bScore = (b.isFavorite ? 1000 : 0) + 
                        (b.lastTimePlayed ? 500 : 0) + 
                        (b.executablePath ? 100 : 0);
          
          if (aScore !== bScore) return bScore - aScore;
          return a.title.localeCompare(b.title);
        });
      
      case 'visible':
      default:
        return games; // Keep original order for visible priority
    }
  }, [priority]);

  // Preload images in batches
  const preloadBatch = useCallback(async (urls: string[], signal: AbortSignal) => {
    const promises = urls.map(async (url) => {
      if (signal.aborted || preloadedUrls.current.has(url) || failedUrls.current.has(url)) {
        return;
      }

      try {
        await new Promise((resolve, reject) => {
          const img = new Image();
          img.onload = () => {
            preloadedUrls.current.add(url);
            setStats(prev => ({
              ...prev,
              preloadedImages: prev.preloadedImages + 1,
              progress: ((prev.preloadedImages + 1) / prev.totalImages) * 100,
            }));
            resolve(void 0);
          };
          img.onerror = () => {
            failedUrls.current.add(url);
            setStats(prev => ({
              ...prev,
              failedImages: prev.failedImages + 1,
              progress: ((prev.preloadedImages + prev.failedImages + 1) / prev.totalImages) * 100,
            }));
            reject(new Error(`Failed to load ${url}`));
          };
          img.src = url;
          
          if (signal.aborted) {
            reject(new Error('Aborted'));
          }
        });
      } catch (error) {
        // Error already handled in promise
      }
    });

    await Promise.allSettled(promises);
  }, []);

  // Main preloading function
  const preloadImages = useCallback(async () => {
    if (!enabled || isDataSaverEnabled() || stats.isPreloading) {
      return;
    }

    // Abort any existing preloading
    if (abortController.current) {
      abortController.current.abort();
    }

    abortController.current = new AbortController();
    const signal = abortController.current.signal;

    const prioritizedGames = prioritizeImages(games);
    const imageUrls = getImageUrls(prioritizedGames);
    
    preloadQueue.current = imageUrls.filter(
      url => !preloadedUrls.current.has(url) && !failedUrls.current.has(url)
    );

    if (preloadQueue.current.length === 0) {
      return;
    }

    setStats(prev => ({
      ...prev,
      totalImages: imageUrls.length,
      isPreloading: true,
    }));

    startTime.current = Date.now();

    try {
      // Process images in batches
      for (let i = 0; i < preloadQueue.current.length; i += batchSize) {
        if (signal.aborted || isPaused.current) {
          break;
        }

        const batch = preloadQueue.current.slice(i, i + batchSize);
        await preloadBatch(batch, signal);

        // Update estimated time remaining
        const elapsed = Date.now() - startTime.current;
        const processed = preloadedUrls.current.size + failedUrls.current.size;
        const remaining = preloadQueue.current.length - processed;
        const avgTimePerImage = elapsed / processed;
        const estimatedTimeRemaining = remaining * avgTimePerImage;

        setStats(prev => ({
          ...prev,
          estimatedTimeRemaining,
        }));

        // Delay between batches to prevent overwhelming the system
        if (i + batchSize < preloadQueue.current.length && delayBetweenBatches > 0) {
          await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
        }
      }
    } catch (error) {
      console.warn('Image preloading interrupted:', error);
    } finally {
      setStats(prev => ({
        ...prev,
        isPreloading: false,
        estimatedTimeRemaining: 0,
      }));
    }
  }, [
    enabled,
    games,
    prioritizeImages,
    getImageUrls,
    preloadBatch,
    batchSize,
    delayBetweenBatches,
    isDataSaverEnabled,
    stats.isPreloading,
  ]);

  // Auto-preload on idle if enabled
  useEffect(() => {
    if (!preloadOnIdle || !enabled || isDataSaverEnabled()) {
      return;
    }

    const handleIdle = () => {
      if (!stats.isPreloading && games.length > 0) {
        preloadImages();
      }
    };

    // Use requestIdleCallback if available, otherwise setTimeout
    let idleId: number;
    if ('requestIdleCallback' in window) {
      idleId = requestIdleCallback(handleIdle, { timeout: 5000 });
    } else {
      idleId = setTimeout(handleIdle, 1000) as any;
    }

    return () => {
      if ('cancelIdleCallback' in window) {
        cancelIdleCallback(idleId);
      } else {
        clearTimeout(idleId);
      }
    };
  }, [preloadOnIdle, enabled, isDataSaverEnabled, stats.isPreloading, games.length, preloadImages]);

  const pausePreloading = useCallback(() => {
    isPaused.current = true;
  }, []);

  const resumePreloading = useCallback(() => {
    isPaused.current = false;
    if (stats.isPreloading) {
      preloadImages();
    }
  }, [stats.isPreloading, preloadImages]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortController.current) {
        abortController.current.abort();
      }
    };
  }, []);

  return {
    ...stats,
    preloadImages,
    pausePreloading,
    resumePreloading,
    clearCache,
  };
}
