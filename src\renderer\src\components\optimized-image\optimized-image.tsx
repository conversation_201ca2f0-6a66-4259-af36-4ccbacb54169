import React, { memo, useCallback, useState, useEffect } from 'react';
import { useImageCache } from '@renderer/hooks/use-image-cache';
import './optimized-image.scss';

interface OptimizedImageProps {
  src?: string;
  alt: string;
  className?: string;
  placeholder?: React.ReactNode;
  fallbackSrc?: string;
  onLoad?: () => void;
  onError?: () => void;
  loading?: 'lazy' | 'eager';
  objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none';
  objectPosition?: string;
  showLoadingSpinner?: boolean;
  fadeInDuration?: number;
}

export const OptimizedImage = memo<OptimizedImageProps>(({
  src,
  alt,
  className = '',
  placeholder,
  fallbackSrc,
  onLoad,
  onError,
  loading = 'lazy',
  objectFit = 'cover',
  objectPosition = 'center',
  showLoadingSpinner = true,
  fadeInDuration = 200,
}) => {
  const { isLoading, isLoaded, hasError, cachedUrl } = useImageCache(src);
  const [showImage, setShowImage] = useState(false);
  const [currentSrc, setCurrentSrc] = useState<string | undefined>(src);
  const [hasFallbackError, setHasFallbackError] = useState(false);

  // Reset states when src changes
  useEffect(() => {
    setShowImage(false);
    setHasFallbackError(false);
    setCurrentSrc(src);
  }, [src]);

  // Handle successful image load
  const handleImageLoad = useCallback(() => {
    setShowImage(true);
    onLoad?.();
  }, [onLoad]);

  // Handle image error
  const handleImageError = useCallback(() => {
    if (fallbackSrc && !hasFallbackError && currentSrc !== fallbackSrc) {
      setHasFallbackError(true);
      setCurrentSrc(fallbackSrc);
    } else {
      onError?.();
    }
  }, [fallbackSrc, hasFallbackError, currentSrc, onError]);

  // Determine what to show
  const shouldShowPlaceholder = !src || (!isLoaded && !hasError);
  const shouldShowSpinner = isLoading && showLoadingSpinner && !placeholder;
  const shouldShowImage = isLoaded && cachedUrl && !hasError;

  return (
    <div className={`optimized-image ${className}`}>
      {/* Placeholder */}
      {shouldShowPlaceholder && placeholder && (
        <div className="optimized-image__placeholder">
          {placeholder}
        </div>
      )}

      {/* Loading spinner */}
      {shouldShowSpinner && (
        <div className="optimized-image__loading">
          <div className="optimized-image__spinner" />
        </div>
      )}

      {/* Actual image */}
      {shouldShowImage && (
        <img
          src={cachedUrl}
          alt={alt}
          className={`optimized-image__img ${showImage ? 'optimized-image__img--visible' : ''}`}
          style={{
            objectFit,
            objectPosition,
            transitionDuration: `${fadeInDuration}ms`,
          }}
          loading={loading}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      )}

      {/* Fallback for errors */}
      {hasError && !fallbackSrc && placeholder && (
        <div className="optimized-image__error">
          {placeholder}
        </div>
      )}
    </div>
  );
});

OptimizedImage.displayName = 'OptimizedImage';
