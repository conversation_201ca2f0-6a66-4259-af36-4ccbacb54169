@use "../../../../scss/globals.scss";

.library-quick-actions {
  display: flex;
  align-items: center;
  gap: calc(globals.$spacing-unit * 1);
  padding: calc(globals.$spacing-unit * 1);

  &__button {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.5);
    padding: calc(globals.$spacing-unit * 1) calc(globals.$spacing-unit * 1.5);
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: globals.$muted-color;
    font-size: globals.$small-font-size;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 32px;

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }

    &--primary {
      background: globals.$brand-teal;
      color: white;
      font-weight: 600;

      &:hover {
        background: lighten(globals.$brand-teal, 10%);
      }
    }

    &--icon {
      padding: calc(globals.$spacing-unit * 1);
      min-width: 32px;
      justify-content: center;

      span {
        display: none;
      }
    }

    &--active {
      background: rgba(255, 255, 255, 0.2);
      color: globals.$brand-teal;
    }
  }

  &__secondary {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.5);
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    &__button {
      padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit * 1);
      font-size: 12px;
      min-height: 28px;

      &--icon {
        padding: calc(globals.$spacing-unit * 0.75);
        min-width: 28px;
      }
    }
  }
}
