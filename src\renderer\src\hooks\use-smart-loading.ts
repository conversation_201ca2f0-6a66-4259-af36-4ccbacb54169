import { useState, useEffect, useCallback, useRef } from 'react';

interface UseSmartLoadingOptions {
  initialDelay?: number;
  minimumLoadingTime?: number;
  enableSkeletonOptimization?: boolean;
  enableProgressiveLoading?: boolean;
  batchSize?: number;
}

interface SmartLoadingState {
  isLoading: boolean;
  isInitialLoad: boolean;
  loadingProgress: number;
  showSkeleton: boolean;
  error: string | null;
}

interface SmartLoadingActions {
  startLoading: (operation?: string) => void;
  finishLoading: () => void;
  setError: (error: string | null) => void;
  setProgress: (progress: number) => void;
  reset: () => void;
}

/**
 * Smart loading hook with progressive loading, skeleton optimization, and UX improvements
 * Provides intelligent loading states that improve perceived performance
 */
export function useSmartLoading(
  options: UseSmartLoadingOptions = {}
): SmartLoadingState & SmartLoadingActions {
  const {
    initialDelay = 200,
    minimumLoadingTime = 500,
    enableSkeletonOptimization = true,
    enableProgressiveLoading = true,
    batchSize = 20,
  } = options;

  const [state, setState] = useState<SmartLoadingState>({
    isLoading: false,
    isInitialLoad: true,
    loadingProgress: 0,
    showSkeleton: false,
    error: null,
  });

  const loadingStartTime = useRef<number>(0);
  const skeletonTimer = useRef<NodeJS.Timeout>();
  const minimumTimer = useRef<NodeJS.Timeout>();
  const progressTimer = useRef<NodeJS.Timeout>();

  // Clear timers on unmount
  useEffect(() => {
    return () => {
      if (skeletonTimer.current) clearTimeout(skeletonTimer.current);
      if (minimumTimer.current) clearTimeout(minimumTimer.current);
      if (progressTimer.current) clearTimeout(progressTimer.current);
    };
  }, []);

  const startLoading = useCallback((operation?: string) => {
    loadingStartTime.current = Date.now();
    
    setState(prev => ({
      ...prev,
      isLoading: true,
      loadingProgress: 0,
      error: null,
    }));

    // Show skeleton after initial delay to avoid flash for fast operations
    if (enableSkeletonOptimization) {
      skeletonTimer.current = setTimeout(() => {
        setState(prev => ({
          ...prev,
          showSkeleton: true,
        }));
      }, initialDelay);
    } else {
      setState(prev => ({
        ...prev,
        showSkeleton: true,
      }));
    }

    // Progressive loading simulation for better UX
    if (enableProgressiveLoading) {
      let progress = 0;
      const progressIncrement = () => {
        progress += Math.random() * 15 + 5; // Random increment between 5-20%
        if (progress < 90) {
          setState(prev => ({
            ...prev,
            loadingProgress: Math.min(progress, 90),
          }));
          progressTimer.current = setTimeout(progressIncrement, 100 + Math.random() * 200);
        }
      };
      progressTimer.current = setTimeout(progressIncrement, 100);
    }

    console.log(`🔄 Smart loading started${operation ? ` for ${operation}` : ''}`);
  }, [initialDelay, enableSkeletonOptimization, enableProgressiveLoading]);

  const finishLoading = useCallback(() => {
    const loadingDuration = Date.now() - loadingStartTime.current;
    const remainingTime = Math.max(0, minimumLoadingTime - loadingDuration);

    // Clear progress timer
    if (progressTimer.current) {
      clearTimeout(progressTimer.current);
    }

    // Complete progress
    setState(prev => ({
      ...prev,
      loadingProgress: 100,
    }));

    // Ensure minimum loading time for better UX (prevents jarring quick flashes)
    const finishLoadingState = () => {
      setState(prev => ({
        ...prev,
        isLoading: false,
        isInitialLoad: false,
        showSkeleton: false,
        loadingProgress: 0,
      }));

      // Clear skeleton timer if still pending
      if (skeletonTimer.current) {
        clearTimeout(skeletonTimer.current);
      }

      console.log(`✅ Smart loading finished (${loadingDuration}ms)`);
    };

    if (remainingTime > 0) {
      minimumTimer.current = setTimeout(finishLoadingState, remainingTime);
    } else {
      finishLoadingState();
    }
  }, [minimumLoadingTime]);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({
      ...prev,
      error,
      isLoading: false,
      showSkeleton: false,
      loadingProgress: 0,
    }));

    // Clear all timers
    if (skeletonTimer.current) clearTimeout(skeletonTimer.current);
    if (minimumTimer.current) clearTimeout(minimumTimer.current);
    if (progressTimer.current) clearTimeout(progressTimer.current);

    if (error) {
      console.error(`❌ Smart loading error: ${error}`);
    }
  }, []);

  const setProgress = useCallback((progress: number) => {
    setState(prev => ({
      ...prev,
      loadingProgress: Math.max(0, Math.min(100, progress)),
    }));
  }, []);

  const reset = useCallback(() => {
    setState({
      isLoading: false,
      isInitialLoad: true,
      loadingProgress: 0,
      showSkeleton: false,
      error: null,
    });

    // Clear all timers
    if (skeletonTimer.current) clearTimeout(skeletonTimer.current);
    if (minimumTimer.current) clearTimeout(minimumTimer.current);
    if (progressTimer.current) clearTimeout(progressTimer.current);
  }, []);

  return {
    ...state,
    startLoading,
    finishLoading,
    setError,
    setProgress,
    reset,
  };
}

/**
 * Hook for batch loading with smart progress tracking
 */
export function useSmartBatchLoading<T>(
  items: T[],
  processor: (item: T) => Promise<void>,
  options: UseSmartLoadingOptions & { 
    onBatchComplete?: (batch: T[]) => void;
    onAllComplete?: () => void;
  } = {}
) {
  const { batchSize = 20, onBatchComplete, onAllComplete, ...loadingOptions } = options;
  const smartLoading = useSmartLoading(loadingOptions);
  const [processedItems, setProcessedItems] = useState<T[]>([]);
  const [currentBatch, setCurrentBatch] = useState(0);

  const processBatch = useCallback(async () => {
    if (items.length === 0) return;

    smartLoading.startLoading('batch processing');
    
    try {
      const totalBatches = Math.ceil(items.length / batchSize);
      
      for (let i = 0; i < totalBatches; i++) {
        const batchStart = i * batchSize;
        const batchEnd = Math.min(batchStart + batchSize, items.length);
        const batch = items.slice(batchStart, batchEnd);
        
        setCurrentBatch(i + 1);
        smartLoading.setProgress((i / totalBatches) * 100);
        
        // Process batch items in parallel
        await Promise.all(batch.map(processor));
        
        setProcessedItems(prev => [...prev, ...batch]);
        onBatchComplete?.(batch);
        
        // Small delay between batches to prevent blocking
        if (i < totalBatches - 1) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }
      
      onAllComplete?.();
      smartLoading.finishLoading();
    } catch (error) {
      smartLoading.setError(error instanceof Error ? error.message : 'Batch processing failed');
    }
  }, [items, processor, batchSize, smartLoading, onBatchComplete, onAllComplete]);

  const reset = useCallback(() => {
    setProcessedItems([]);
    setCurrentBatch(0);
    smartLoading.reset();
  }, [smartLoading]);

  return {
    ...smartLoading,
    processedItems,
    currentBatch,
    totalBatches: Math.ceil(items.length / batchSize),
    processBatch,
    reset,
  };
}
