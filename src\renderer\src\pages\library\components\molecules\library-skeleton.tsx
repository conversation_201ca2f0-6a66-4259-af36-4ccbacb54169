import "./library-skeleton.scss";

interface LibrarySkeletonProps {
  viewMode: "grid" | "list";
  count?: number;
}

export function LibrarySkeleton({ viewMode, count = 12 }: LibrarySkeletonProps) {
  const skeletonItems = Array.from({ length: count }, (_, index) => index);

  if (viewMode === "grid") {
    return (
      <div className="library-skeleton library-skeleton--grid">
        {skeletonItems.map((index) => (
          <div key={index} className="library-skeleton__card">
            <div className="library-skeleton__image"></div>
            <div className="library-skeleton__content">
              <div className="library-skeleton__title"></div>
              <div className="library-skeleton__subtitle"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="library-skeleton library-skeleton--list">
      <div className="library-skeleton__list-header">
        <div className="library-skeleton__header-cell library-skeleton__header-cell--main"></div>
        <div className="library-skeleton__header-cell"></div>
        <div className="library-skeleton__header-cell"></div>
        <div className="library-skeleton__header-cell"></div>
      </div>
      <div className="library-skeleton__list-content">
        {skeletonItems.map((index) => (
          <div key={index} className="library-skeleton__list-item">
            <div className="library-skeleton__list-game">
              <div className="library-skeleton__list-image"></div>
              <div className="library-skeleton__list-title"></div>
            </div>
            <div className="library-skeleton__list-cell"></div>
            <div className="library-skeleton__list-cell"></div>
            <div className="library-skeleton__list-cell"></div>
          </div>
        ))}
      </div>
    </div>
  );
}
