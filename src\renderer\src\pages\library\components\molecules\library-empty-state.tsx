import { useTranslation } from "react-i18next";
import { BookIcon, SearchIcon, PlusIcon } from "@primer/octicons-react";
import { Button } from "@renderer/components";
import "./library-empty-state.scss";

interface LibraryEmptyStateProps {
  type: "empty-library" | "no-search-results" | "empty-collection";
  searchQuery?: string;
  collectionName?: string;
  onClearSearch?: () => void;
  onAddGames?: () => void;
}

export function LibraryEmptyState({
  type,
  searchQuery,
  collectionName,
  onClearSearch,
  onAddGames,
}: LibraryEmptyStateProps) {
  const { t } = useTranslation("library");

  const getEmptyStateContent = () => {
    switch (type) {
      case "empty-library":
        return {
          icon: <BookIcon size={48} />,
          title: t("empty_library_title"),
          subtitle: t("empty_library_subtitle"),
          action: onAddGames && (
            <Button onClick={onAddGames} theme="primary">
              <PlusIcon size={16} />
              {t("add_games")}
            </Button>
          ),
        };

      case "no-search-results":
        return {
          icon: <SearchIcon size={48} />,
          title: t("no_search_results_title"),
          subtitle: t("no_search_results_subtitle", { query: searchQuery }),
          action: onClearSearch && (
            <Button onClick={onClearSearch} theme="outline">
              {t("clear_search")}
            </Button>
          ),
        };

      case "empty-collection":
        return {
          icon: <BookIcon size={48} />,
          title: t("empty_collection_title"),
          subtitle: t("empty_collection_subtitle", { name: collectionName }),
          action: onAddGames && (
            <Button onClick={onAddGames} theme="primary">
              <PlusIcon size={16} />
              {t("add_games_to_collection")}
            </Button>
          ),
        };

      default:
        return {
          icon: <BookIcon size={48} />,
          title: t("empty_state_title"),
          subtitle: t("empty_state_subtitle"),
        };
    }
  };

  const { icon, title, subtitle, action } = getEmptyStateContent();

  return (
    <div className="library-empty-state">
      <div className="library-empty-state__content">
        <div className="library-empty-state__icon">{icon}</div>
        <h3 className="library-empty-state__title">{title}</h3>
        <p className="library-empty-state__subtitle">{subtitle}</p>
        {action && <div className="library-empty-state__action">{action}</div>}
      </div>
    </div>
  );
}
