import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { XIcon, DownloadIcon } from "@primer/octicons-react";

import { Modal } from "@renderer/components";
import type { LibraryGame, GameRepack } from "@types";

interface LibraryRepacksModalProps {
  visible: boolean;
  game: LibraryGame;
  repacks: GameRepack[];
  startDownload: (repack: GameRepack) => void;
  onClose: () => void;
}

export function LibraryRepacksModal({
  visible,
  game,
  repacks,
  startDownload,
  onClose,
}: LibraryRepacksModalProps) {
  const { t } = useTranslation("library");

  const handleDownload = useCallback(
    (repack: GameRepack) => {
      startDownload(repack);
      onClose();
    },
    [startDownload, onClose]
  );

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      title={t("select_repack")}
      onClose={onClose}
      className="library-repacks-modal"
    >
      <div className="library-repacks-modal__content">
        <div className="library-repacks-modal__header">
          <h3>{game.title}</h3>
          <p>{t("available_repacks", { count: repacks.length })}</p>
        </div>

        <div className="library-repacks-modal__list">
          {repacks.length === 0 ? (
            <div className="library-repacks-modal__empty">
              <p>{t("no_repacks_available")}</p>
            </div>
          ) : (
            repacks.map((repack, index) => (
              <div key={index} className="library-repacks-modal__item">
                <div className="library-repacks-modal__item-info">
                  <div className="library-repacks-modal__item-title">
                    {repack.title || `${t("repack")} ${index + 1}`}
                  </div>
                  {repack.fileSize && (
                    <div className="library-repacks-modal__item-size">
                      {repack.fileSize}
                    </div>
                  )}
                  {repack.uploadDate && (
                    <div className="library-repacks-modal__item-date">
                      {new Date(repack.uploadDate).toLocaleDateString()}
                    </div>
                  )}
                </div>
                <button
                  type="button"
                  className="library-repacks-modal__download-button"
                  onClick={() => handleDownload(repack)}
                >
                  <DownloadIcon size={16} />
                  {t("download")}
                </button>
              </div>
            ))
          )}
        </div>
      </div>
    </Modal>
  );
}
