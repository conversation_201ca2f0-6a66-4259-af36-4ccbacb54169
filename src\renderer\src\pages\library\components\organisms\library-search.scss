@use "../../../../scss/globals.scss";

/**
 * LibrarySearch Styles
 *
 * Touch-optimized search component for library games.
 * Features autocomplete suggestions and responsive design.
 *
 * Key Features:
 * - Touch-friendly input (48px+ minimum height)
 * - Steam Deck optimizations (52px+ targets)
 * - Smooth animations and transitions
 * - Accessible keyboard navigation
 * - Responsive dropdown suggestions
 * - Visual feedback for search states
 */

.library-search {
  position: relative;
  width: 100%;

  &__input-wrapper {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 12px;
    padding: 0 calc(globals.$spacing-unit * 1.5);
    min-height: 48px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(16px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(255, 255, 255, 0.2);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    &--focused {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.3);
      box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
    }

    &--has-value {
      .library-search__search-icon {
        color: globals.$muted-color;
      }
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      min-height: 52px;
      padding: 0 calc(globals.$spacing-unit * 2);
    }

    @media (max-width: 768px) {
      min-height: 44px;
      padding: 0 calc(globals.$spacing-unit * 1.25);
    }
  }

  &__search-icon {
    color: globals.$body-color;
    margin-right: calc(globals.$spacing-unit * 1);
    flex-shrink: 0;
    transition: color 0.2s ease;
    width: 16px;
    height: 16px;

    @media (max-width: 768px) {
      width: 14px;
      height: 14px;
      margin-right: calc(globals.$spacing-unit * 0.75);
    }
  }

  &__input {
    flex: 1;
    background: transparent;
    border: none;
    outline: none;
    color: globals.$muted-color;
    font-size: globals.$body-font-size;
    font-family: inherit;
    padding: 0;
    min-height: 24px;

    &::placeholder {
      color: globals.$body-color;
      opacity: 0.7;
    }

    &:focus::placeholder {
      opacity: 0.5;
    }

    @media (max-width: 768px) {
      font-size: 14px;
    }
  }

  &__clear-button {
    background: none;
    border: none;
    color: globals.$body-color;
    cursor: pointer;
    padding: calc(globals.$spacing-unit * 0.5);
    margin-left: calc(globals.$spacing-unit * 0.5);
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    min-height: 24px;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: globals.$muted-color;
    }

    &:active {
      transform: scale(0.95);
    }

    svg {
      width: 14px;
      height: 14px;
    }

    // Enhanced touch targets for mobile
    @media (max-width: 768px) {
      min-width: 32px;
      min-height: 32px;
      padding: calc(globals.$spacing-unit * 0.75);
    }
  }

  &__dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    margin-top: calc(globals.$spacing-unit * 0.5);
    background: globals.$background-color;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    overflow: hidden;
    animation: dropdownSlideIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    @keyframes dropdownSlideIn {
      from {
        opacity: 0;
        transform: translateY(-8px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  &__suggestions {
    max-height: 300px;
    overflow-y: auto;
    padding: calc(globals.$spacing-unit * 0.5);

    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 3px;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  &__suggestion {
    width: 100%;
    background: none;
    border: none;
    padding: calc(globals.$spacing-unit * 1.25);
    text-align: left;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: block;
    min-height: 48px;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      transform: translateX(4px);
    }

    &:active {
      background: rgba(255, 255, 255, 0.12);
      transform: translateX(2px);
    }

    // Enhanced touch targets for mobile
    @media (max-width: 768px) {
      min-height: 52px;
      padding: calc(globals.$spacing-unit * 1.5);
    }
  }

  &__suggestion-content {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 0.25);
  }

  &__suggestion-title {
    color: globals.$muted-color;
    font-size: globals.$body-font-size;
    font-weight: 500;
    line-height: 1.4;

    // Highlight matching text
    mark {
      background: rgba(255, 255, 255, 0.2);
      color: inherit;
      padding: 0 2px;
      border-radius: 3px;
      font-weight: 600;
    }

    @media (max-width: 768px) {
      font-size: 14px;
    }
  }

  &__suggestion-type {
    color: globals.$body-color;
    font-size: globals.$small-font-size;
    opacity: 0.7;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;

    @media (max-width: 768px) {
      font-size: 11px;
    }
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    &__dropdown {
      margin-top: calc(globals.$spacing-unit * 0.25);
      border-radius: 8px;
    }

    &__suggestions {
      max-height: 250px;
      padding: calc(globals.$spacing-unit * 0.25);
    }
  }

  // High contrast mode support
  @media (prefers-contrast: high) {
    &__input-wrapper {
      border-color: rgba(255, 255, 255, 0.5);
      
      &--focused {
        border-color: rgba(255, 255, 255, 0.8);
      }
    }

    &__dropdown {
      border-color: rgba(255, 255, 255, 0.5);
    }
  }

  // Reduced motion support
  @media (prefers-reduced-motion: reduce) {
    &__input-wrapper,
    &__clear-button,
    &__suggestion {
      transition: none;
    }

    &__dropdown {
      animation: none;
    }
  }
}
