@use "../../../../scss/globals.scss";

.library-skeleton {
  animation: fadeIn 0.3s ease-out;

  &--grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 2);

    @media (min-width: 1400px) {
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: globals.$spacing-unit;
      padding: globals.$spacing-unit;
    }
  }

  &--list {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.03), rgba(255, 255, 255, 0.01));
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    overflow: hidden;
    backdrop-filter: blur(10px);
  }

  &__card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    overflow: hidden;
    backdrop-filter: blur(10px);
  }

  &__image {
    width: 100%;
    height: 160px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.15) 50%,
      rgba(255, 255, 255, 0.1) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  &__content {
    padding: calc(globals.$spacing-unit * 2);
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 1.5);
  }

  &__title {
    height: 20px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.15) 50%,
      rgba(255, 255, 255, 0.1) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
    width: 80%;
  }

  &__subtitle {
    height: 14px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.08) 0%,
      rgba(255, 255, 255, 0.12) 50%,
      rgba(255, 255, 255, 0.08) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite 0.2s;
    border-radius: 4px;
    width: 60%;
  }

  &__list-header {
    display: grid;
    grid-template-columns: 1fr auto auto auto;
    gap: calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 2) calc(globals.$spacing-unit * 2.5);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  &__header-cell {
    height: 16px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.15) 50%,
      rgba(255, 255, 255, 0.1) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;

    &--main {
      width: 40%;
    }
  }

  &__list-content {
    display: flex;
    flex-direction: column;
  }

  &__list-item {
    display: grid;
    grid-template-columns: 1fr auto auto auto;
    gap: calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 2) calc(globals.$spacing-unit * 2.5);
    border-bottom: 1px solid rgba(255, 255, 255, 0.06);
    align-items: center;

    &:last-child {
      border-bottom: none;
    }
  }

  &__list-game {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.5);
  }

  &__list-image {
    width: 48px;
    height: 48px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.15) 50%,
      rgba(255, 255, 255, 0.1) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
  }

  &__list-title {
    height: 16px;
    width: 200px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.15) 50%,
      rgba(255, 255, 255, 0.1) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite 0.1s;
    border-radius: 4px;
  }

  &__list-cell {
    height: 16px;
    width: 80px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.08) 0%,
      rgba(255, 255, 255, 0.12) 50%,
      rgba(255, 255, 255, 0.08) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite 0.3s;
    border-radius: 4px;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
