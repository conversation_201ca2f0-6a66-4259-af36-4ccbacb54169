.optimized-image {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  &__placeholder,
  &__loading,
  &__error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 100%);
  }

  &__img {
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.2s ease;
    will-change: opacity;

    &--visible {
      opacity: 1;
    }
  }

  &__spinner {
    width: 24px;
    height: 24px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-top: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: optimized-image-spin 1s linear infinite;
  }
}

@keyframes optimized-image-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
