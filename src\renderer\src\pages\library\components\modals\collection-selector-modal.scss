@use "../../../../scss/globals.scss";

.collection-selector-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: backdropFadeIn 0.3s ease-out;
}

.collection-selector-modal {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 20px;
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(40px);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 8px 32px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: calc(globals.$spacing-unit * 4) calc(globals.$spacing-unit * 4) calc(globals.$spacing-unit * 3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  }

  &__title-section {
    flex: 1;
    min-width: 0;
  }

  &__title {
    margin: 0 0 calc(globals.$spacing-unit * 0.5) 0;
    font-size: 24px;
    font-weight: 700;
    color: globals.$muted-color;
    background: linear-gradient(135deg, globals.$muted-color 0%, rgba(255, 255, 255, 0.8) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: -0.01em;
  }

  &__subtitle {
    margin: 0;
    font-size: globals.$body-font-size;
    color: globals.$body-color;
    font-weight: 500;
    opacity: 0.8;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__close {
    background: transparent;
    border: none;
    color: globals.$body-color;
    cursor: pointer;
    padding: calc(globals.$spacing-unit * 0.75);
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: calc(globals.$spacing-unit * 2);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: globals.$muted-color;
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  &__content {
    flex: 1;
    overflow-y: auto;
    padding: calc(globals.$spacing-unit * 3) calc(globals.$spacing-unit * 4);

    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 3px;
      transition: background 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 6);
    text-align: center;

    svg {
      color: globals.$body-color;
      opacity: 0.5;
    }

    h3 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: globals.$muted-color;
    }

    p {
      margin: 0;
      color: globals.$body-color;
      max-width: 300px;
      line-height: 1.5;
    }
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit * 1.5);
  }

  &__item {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 2);
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    width: 100%;

    &:hover {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.12);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);

      .collection-selector-modal__item-name {
        color: globals.$muted-color;
      }

      .collection-selector-modal__item-description {
        color: rgba(192, 193, 199, 0.9);
      }

      .collection-selector-modal__item-count {
        background: rgba(255, 255, 255, 0.1);
        color: globals.$muted-color;
      }

      .collection-selector-modal__item-checkbox {
        border-color: rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.03);
      }
    }

    &--selected {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.92) 0%, rgba(255, 255, 255, 0.88) 100%);
      border-color: rgba(255, 255, 255, 0.35);
      box-shadow: 0 2px 12px rgba(255, 255, 255, 0.15);
      color: globals.$dark-background-color;

      .collection-selector-modal__item-name {
        color: globals.$dark-background-color;
        font-weight: 600;
      }

      .collection-selector-modal__item-description {
        color: rgba(21, 21, 21, 0.75);
      }

      .collection-selector-modal__item-count {
        background: rgba(21, 21, 21, 0.08);
        color: globals.$dark-background-color;
        font-weight: 600;
      }

      .collection-selector-modal__item-checkbox {
        background: globals.$dark-background-color;
        color: white;
        border-color: globals.$dark-background-color;
      }

      &:hover {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
        border-color: rgba(255, 255, 255, 0.4);
        box-shadow: 0 4px 16px rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);

        .collection-selector-modal__item-name {
          color: globals.$dark-background-color;
        }

        .collection-selector-modal__item-description {
          color: rgba(21, 21, 21, 0.8);
        }

        .collection-selector-modal__item-count {
          background: rgba(21, 21, 21, 0.1);
          color: globals.$dark-background-color;
        }

        .collection-selector-modal__item-checkbox {
          background: globals.$dark-background-color;
          color: white;
          border-color: globals.$dark-background-color;
        }
      }
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none !important;
    }
  }

  &__item-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  &__item-info {
    flex: 1;
    min-width: 0;
  }

  &__item-name {
    margin: 0 0 calc(globals.$spacing-unit * 0.25) 0;
    font-size: globals.$body-font-size;
    font-weight: 600;
    color: globals.$muted-color;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: color 0.2s ease;
  }

  &__item-description {
    margin: 0 0 calc(globals.$spacing-unit * 0.5) 0;
    font-size: globals.$small-font-size;
    color: globals.$body-color;
    opacity: 0.85;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: color 0.2s ease;
    line-height: 1.4;
  }

  &__item-count {
    font-size: globals.$small-font-size;
    color: globals.$body-color;
    opacity: 0.7;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.06);
    padding: calc(globals.$spacing-unit / 3) calc(globals.$spacing-unit / 1.5);
    border-radius: 8px;
    transition: all 0.2s ease;
    border: 1px solid rgba(255, 255, 255, 0.04);
  }

  &__item-checkbox {
    width: 24px;
    height: 24px;
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
    background: rgba(255, 255, 255, 0.02);

    svg {
      color: white;
      opacity: 0.9;
    }
  }

  &__actions {
    margin-top: calc(globals.$spacing-unit * 3);
    padding-top: calc(globals.$spacing-unit * 2);
    border-top: 1px solid rgba(255, 255, 255, 0.08);
  }

  &__footer {
    display: flex;
    justify-content: flex-end;
    padding: calc(globals.$spacing-unit * 3) calc(globals.$spacing-unit * 4);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
  }
}

// Animations
@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(-40px) rotateX(10deg);
    filter: blur(4px);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02) translateY(-10px) rotateX(0deg);
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0) rotateX(0deg);
    filter: blur(0);
  }
}

// Stagger animation for collection items
.collection-selector-modal__item {
  animation: itemSlideIn 0.4s ease-out;
  animation-fill-mode: both;

  @for $i from 1 through 10 {
    &:nth-child(#{$i}) {
      animation-delay: #{0.05s * $i};
    }
  }
}

@keyframes itemSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
