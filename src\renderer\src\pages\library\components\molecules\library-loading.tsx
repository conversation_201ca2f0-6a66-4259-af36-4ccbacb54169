import { useTranslation } from "react-i18next";
import "./library-loading.scss";

export function LibraryLoading() {
  const { t } = useTranslation("library");

  return (
    <div className="library-loading">
      <div className="library-loading__content">
        <div className="library-loading__spinner">
          <div className="library-loading__spinner-ring"></div>
          <div className="library-loading__spinner-ring"></div>
          <div className="library-loading__spinner-ring"></div>
        </div>
        <h3 className="library-loading__title">{t("loading_library")}</h3>
        <p className="library-loading__subtitle">{t("loading_collections")}</p>
      </div>
    </div>
  );
}
