import { useState, useRef, useEffect, useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { SearchIcon, XIcon } from "@primer/octicons-react";
import cn from "classnames";

import type { LibraryGame } from "@types";

import "./library-search.scss";

interface LibrarySearchProps {
  value: string;
  onChange: (value: string) => void;
  games: LibraryGame[];
  className?: string;
  placeholder?: string;
  showSuggestions?: boolean;
  maxSuggestions?: number;
}

interface SearchSuggestion {
  game: LibraryGame;
  matchType: "title" | "genre";
  highlightedText: string;
}

export function LibrarySearch({
  value,
  onChange,
  games,
  className,
  placeholder,
  showSuggestions = true,
  maxSuggestions = 5,
}: LibrarySearchProps) {
  const { t } = useTranslation("library");
  const [isFocused, setIsFocused] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Generate search suggestions based on current input
  const suggestions = useMemo(() => {
    if (!value || value.length < 2 || !showSuggestions) return [];

    const query = value.toLowerCase();
    const results: SearchSuggestion[] = [];

    games.forEach((game) => {
      // Title matching
      if (game.title.toLowerCase().includes(query)) {
        const titleIndex = game.title.toLowerCase().indexOf(query);
        const highlightedText = 
          game.title.substring(0, titleIndex) +
          `<mark>${game.title.substring(titleIndex, titleIndex + value.length)}</mark>` +
          game.title.substring(titleIndex + value.length);

        results.push({
          game,
          matchType: "title",
          highlightedText,
        });
      }
    });

    return results.slice(0, maxSuggestions);
  }, [value, games, showSuggestions, maxSuggestions]);

  const handleInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value;
      onChange(newValue);
      setShowDropdown(newValue.length >= 2 && suggestions.length > 0);
    },
    [onChange, suggestions.length]
  );

  const handleInputFocus = useCallback(() => {
    setIsFocused(true);
    if (value.length >= 2 && suggestions.length > 0) {
      setShowDropdown(true);
    }
  }, [value.length, suggestions.length]);

  const handleInputBlur = useCallback(() => {
    setIsFocused(false);
    // Delay hiding dropdown to allow for suggestion clicks
    setTimeout(() => setShowDropdown(false), 150);
  }, []);

  const handleClearSearch = useCallback(() => {
    onChange("");
    setShowDropdown(false);
    inputRef.current?.focus();
  }, [onChange]);

  const handleSuggestionClick = useCallback(
    (suggestion: SearchSuggestion) => {
      onChange(suggestion.game.title);
      setShowDropdown(false);
      inputRef.current?.blur();
    },
    [onChange]
  );

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === "Escape") {
        setShowDropdown(false);
        inputRef.current?.blur();
      }
    },
    []
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className={cn("library-search", className)}>
      <div
        className={cn("library-search__input-wrapper", {
          "library-search__input-wrapper--focused": isFocused,
          "library-search__input-wrapper--has-value": value.length > 0,
        })}
      >
        <SearchIcon className="library-search__search-icon" />
        
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder || t("search_games")}
          className="library-search__input"
          autoComplete="off"
          spellCheck={false}
        />

        {value && (
          <button
            type="button"
            onClick={handleClearSearch}
            className="library-search__clear-button"
            aria-label={t("clear_search")}
          >
            <XIcon />
          </button>
        )}
      </div>

      {/* Search Suggestions Dropdown */}
      {showDropdown && suggestions.length > 0 && (
        <div ref={dropdownRef} className="library-search__dropdown">
          <div className="library-search__suggestions">
            {suggestions.map((suggestion, index) => (
              <button
                key={`${suggestion.game.objectId}-${index}`}
                type="button"
                className="library-search__suggestion"
                onClick={() => handleSuggestionClick(suggestion)}
              >
                <div className="library-search__suggestion-content">
                  <span
                    className="library-search__suggestion-title"
                    dangerouslySetInnerHTML={{ __html: suggestion.highlightedText }}
                  />
                  <span className="library-search__suggestion-type">
                    {suggestion.matchType === "title" ? t("game_title") : t("genre")}
                  </span>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
