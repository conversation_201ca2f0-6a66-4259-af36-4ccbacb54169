import { useCallback, useEffect, useState } from 'react';

interface ImageCacheEntry {
  url: string;
  blob: Blob;
  timestamp: number;
}

interface ImageCacheState {
  isLoading: boolean;
  isLoaded: boolean;
  hasError: boolean;
  cachedUrl?: string;
}

class ImageCache {
  private cache = new Map<string, ImageCacheEntry>();
  private loadingPromises = new Map<string, Promise<string>>();
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours
  private readonly MAX_CACHE_SIZE = 100; // Maximum number of cached images

  async getImage(url: string): Promise<string> {
    // Check if already loading
    const existingPromise = this.loadingPromises.get(url);
    if (existingPromise) {
      return existingPromise;
    }

    // Check cache first
    const cached = this.cache.get(url);
    if (cached && this.isCacheValid(cached)) {
      return URL.createObjectURL(cached.blob);
    }

    // Create loading promise
    const loadingPromise = this.loadAndCacheImage(url);
    this.loadingPromises.set(url, loadingPromise);

    try {
      const result = await loadingPromise;
      return result;
    } finally {
      this.loadingPromises.delete(url);
    }
  }

  private async loadAndCacheImage(url: string): Promise<string> {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status}`);
      }

      const blob = await response.blob();
      
      // Clean cache if needed
      this.cleanCache();

      // Cache the image
      this.cache.set(url, {
        url,
        blob,
        timestamp: Date.now(),
      });

      return URL.createObjectURL(blob);
    } catch (error) {
      console.error('Failed to load and cache image:', error);
      throw error;
    }
  }

  private isCacheValid(entry: ImageCacheEntry): boolean {
    return Date.now() - entry.timestamp < this.CACHE_DURATION;
  }

  private cleanCache(): void {
    if (this.cache.size < this.MAX_CACHE_SIZE) {
      return;
    }

    // Remove expired entries first
    const now = Date.now();
    for (const [url, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.CACHE_DURATION) {
        URL.revokeObjectURL(URL.createObjectURL(entry.blob));
        this.cache.delete(url);
      }
    }

    // If still over limit, remove oldest entries
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const toRemove = entries.slice(0, entries.length - this.MAX_CACHE_SIZE + 10);
      for (const [url, entry] of toRemove) {
        URL.revokeObjectURL(URL.createObjectURL(entry.blob));
        this.cache.delete(url);
      }
    }
  }

  preloadImages(urls: string[]): void {
    urls.forEach(url => {
      if (!this.cache.has(url) && !this.loadingPromises.has(url)) {
        this.getImage(url).catch(() => {
          // Ignore preload errors
        });
      }
    });
  }

  clearCache(): void {
    for (const entry of this.cache.values()) {
      URL.revokeObjectURL(URL.createObjectURL(entry.blob));
    }
    this.cache.clear();
    this.loadingPromises.clear();
  }
}

// Global image cache instance
const imageCache = new ImageCache();

export function useImageCache(url?: string): ImageCacheState & {
  preloadImages: (urls: string[]) => void;
  clearCache: () => void;
} {
  const [state, setState] = useState<ImageCacheState>({
    isLoading: false,
    isLoaded: false,
    hasError: false,
  });

  const loadImage = useCallback(async (imageUrl: string) => {
    setState(prev => ({ ...prev, isLoading: true, hasError: false }));

    try {
      const cachedUrl = await imageCache.getImage(imageUrl);
      setState({
        isLoading: false,
        isLoaded: true,
        hasError: false,
        cachedUrl,
      });
    } catch (error) {
      setState({
        isLoading: false,
        isLoaded: false,
        hasError: true,
      });
    }
  }, []);

  useEffect(() => {
    if (url) {
      loadImage(url);
    } else {
      setState({
        isLoading: false,
        isLoaded: false,
        hasError: false,
      });
    }
  }, [url, loadImage]);

  const preloadImages = useCallback((urls: string[]) => {
    imageCache.preloadImages(urls);
  }, []);

  const clearCache = useCallback(() => {
    imageCache.clearCache();
  }, []);

  return {
    ...state,
    preloadImages,
    clearCache,
  };
}
