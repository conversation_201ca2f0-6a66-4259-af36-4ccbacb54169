@use '../../../../scss/globals.scss' as *;

/**
 * NewsModal Styles
 *
 * Touch-optimized modal for displaying Steam news content.
 * Designed for seamless experience across desktop, tablet, and handheld devices.
 *
 * Key Features:
 * - Touch-friendly modal controls (48px+ minimum)
 * - Steam Deck optimizations (52px+ targets)
 * - Responsive modal sizing and scrolling
 * - Enhanced readability for handheld devices
 * - Accessible keyboard navigation
 * - Smooth animations and transitions
 */

.news-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: calc($spacing-unit * 2);
  animation: fadeIn 0.3s ease-out;
  overflow-y: auto;
  overflow-x: hidden;

  // Steam Deck optimizations
  @media (max-width: 1280px) and (max-height: 800px) {
    padding: calc($spacing-unit * 1.5);
  }

  @media (max-width: 768px) {
    padding: calc($spacing-unit * 1);
  }
}

.news-modal {
  background: $background-color;
  border-radius: 24px;
  width: 100%;
  max-width: 1100px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow:
    0 32px 100px rgba(0, 0, 0, 0.7),
    0 0 0 1px rgba($border-color, 0.15);
  display: flex;
  flex-direction: column;
  position: relative;
  animation: slideInUp 0.5s cubic-bezier(0.16, 1, 0.3, 1);
}

// Close Button - Touch Optimized
.news-modal-close-floating {
  position: absolute;
  top: calc($spacing-unit * 2);
  right: calc($spacing-unit * 2);
  z-index: 10;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  min-width: 56px; // Touch target minimum
  min-height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);

  &:hover {
    background: rgba(0, 0, 0, 0.95);
    border-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.4);
  }

  &:active {
    transform: scale(0.95);
  }

  // Steam Deck optimizations
  @media (max-width: 1280px) and (max-height: 800px) {
    min-width: 60px;
    min-height: 60px;
    top: calc($spacing-unit * 2.5);
    right: calc($spacing-unit * 2.5);
  }

  // Touch feedback for mobile devices
  @media (hover: none) and (pointer: coarse) {
    &:active {
      transform: scale(0.9);
      transition: transform 0.1s ease;
    }
  }
}

// Hero section with featured image
.news-modal-hero {
  position: relative;
  width: 100%;
  height: 400px;
  flex-shrink: 0;
  overflow: hidden;
  background: linear-gradient(135deg, rgba($brand-teal, 0.1), rgba($brand-teal, 0.05));

  &-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center center;
    transition: transform 0.3s ease;
    filter: brightness(0.6);
  }

  &-overlay {
    position: relative;
    z-index: 2;
    height: 100%;
    background: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.95) 0%,
      rgba(0, 0, 0, 0.85) 30%,
      rgba(0, 0, 0, 0.7) 50%,
      rgba(0, 0, 0, 0.5) 70%,
      rgba(0, 0, 0, 0.3) 85%,
      rgba(0, 0, 0, 0.1) 100%
    );
    padding: calc($spacing-unit * 4) calc($spacing-unit * 3) calc($spacing-unit * 3);
    display: flex;
    align-items: flex-end;
  }

  &-content {
    color: white;
    max-width: 900px;
    width: 100%;
  }

  &-meta {
    display: flex;
    align-items: center;
    gap: calc($spacing-unit * 2.5);
    margin-top: calc($spacing-unit * 2);
    flex-wrap: wrap;

    .news-modal-meta-item {
      display: flex;
      align-items: center;
      gap: calc($spacing-unit * 0.75);
      color: rgba(white, 0.9);
      font-size: 0.9rem;
      font-weight: 500;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);

      svg {
        width: 18px;
        height: 18px;
        opacity: 0.8;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5));
      }
    }

    .news-modal-event-meta {
      color: #ffd700;
      font-weight: 600;

      svg {
        opacity: 1;
      }
    }
  }
}

// Breadcrumb navigation
.news-modal-breadcrumb {
  display: flex;
  align-items: center;
  gap: calc($spacing-unit * 0.75);
  margin-bottom: calc($spacing-unit * 1.5);
  font-size: 0.875rem;
  opacity: 0.9;

  &-separator {
    opacity: 0.6;
    font-weight: 300;
  }
}

.news-modal-game-name {
  color: $brand-teal;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.news-modal-news-type {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.75rem;
}

// Main title
.news-modal-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: white;
  margin: 0;
  line-height: 1.1;
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.8),
    0 4px 12px rgba(0, 0, 0, 0.5);
  letter-spacing: -0.02em;
  max-width: 100%;
  word-wrap: break-word;
  hyphens: auto;
}

// Content wrapper
.news-modal-content-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba($body-color, 0.3) transparent;
  scroll-behavior: smooth;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba($background-color, 0.5);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba($body-color, 0.3);
    border-radius: 4px;
    border: 1px solid rgba($background-color, 0.5);

    &:hover {
      background: rgba($body-color, 0.5);
    }

    &:active {
      background: rgba($brand-teal, 0.6);
    }
  }
}

// Article metadata
.news-modal-article-meta {
  padding: calc($spacing-unit * 2.5) calc($spacing-unit * 3) calc($spacing-unit * 1.5);
  border-bottom: 1px solid rgba($border-color, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: calc($spacing-unit * 2);
  background: rgba($background-color, 0.5);
  backdrop-filter: blur(10px);
}

.news-modal-meta-left {
  display: flex;
  align-items: center;
  gap: calc($spacing-unit * 2);
  flex-wrap: wrap;
}

.news-modal-meta-item {
  display: flex;
  align-items: center;
  gap: calc($spacing-unit * 0.5);
  font-size: 0.875rem;
  color: $body-color;
  font-weight: 500;

  svg {
    opacity: 0.7;
  }
}

.news-modal-event-meta {
  color: $brand-teal;
  font-weight: 600;

  svg {
    opacity: 1;
  }
}

// Action buttons
.news-modal-meta-actions {
  display: flex;
  align-items: center;
  gap: calc($spacing-unit * 0.75);
}

// Action Buttons - Touch Optimized
.news-modal-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 48px; // Touch target minimum
  min-height: 48px;
  border: 1px solid rgba($border-color, 0.3);
  border-radius: 14px;
  background: rgba($background-color, 0.8);
  backdrop-filter: blur(16px);
  color: $body-color;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:hover {
    background: rgba($brand-teal, 0.1);
    border-color: rgba($brand-teal, 0.3);
    color: $brand-teal;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba($brand-teal, 0.15);
  }

  &.active {
    background: rgba($brand-teal, 0.15);
    border-color: $brand-teal;
    color: $brand-teal;
    box-shadow: 0 4px 16px rgba($brand-teal, 0.2);
  }

  &:active {
    transform: translateY(0);
  }

  // Steam Deck optimizations
  @media (max-width: 1280px) and (max-height: 800px) {
    min-width: 52px;
    min-height: 52px;
    border-radius: 16px;
  }

  // Touch feedback for mobile devices
  @media (hover: none) and (pointer: coarse) {
    &:active {
      transform: scale(0.95);
      transition: transform 0.1s ease;
    }
  }
}

// Share menu
.news-modal-share-container {
  position: relative;
}

.news-modal-share-menu {
  position: absolute;
  top: calc(100% + calc($spacing-unit * 0.5));
  right: 0;
  background: $background-color;
  border: 1px solid rgba($border-color, 0.3);
  border-radius: 12px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  padding: calc($spacing-unit * 0.5);
  min-width: 160px;
  z-index: 20;
  animation: slideInDown 0.2s ease-out;

  button {
    width: 100%;
    padding: calc($spacing-unit * 0.75) calc($spacing-unit);
    border: none;
    background: transparent;
    color: $body-color;
    text-align: left;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;

    &:hover {
      background: rgba($brand-teal, 0.1);
      color: $brand-teal;
    }
  }
}

// Article content
.news-modal-article {
  flex: 1;
  padding: calc($spacing-unit * 3);
  background: $background-color;
}

.news-modal-content {
  max-width: 800px;
  margin: 0 auto;

  .news-content {
    color: $muted-color;
    line-height: 1.7;
    font-size: 1rem;
    font-weight: 400;

    p {
      margin: 0 0 calc($spacing-unit * 2) 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    h1, h2, h3, h4, h5, h6 {
      color: $muted-color;
      margin: calc($spacing-unit * 3) 0 calc($spacing-unit * 1.5) 0;
      font-weight: 700;
      letter-spacing: -0.01em;

      &:first-child {
        margin-top: 0;
      }
    }

    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }

    strong {
      color: $muted-color;
      font-weight: 700;
    }

    em {
      font-style: italic;
      color: rgba($muted-color, 0.9);
    }

    a {
      color: $brand-teal;
      text-decoration: none;
      border-bottom: 1px solid transparent;
      transition: all 0.2s ease;
      font-weight: 500;

      &:hover {
        border-bottom-color: $brand-teal;
        color: #139b82;
      }
    }

    img {
      max-width: 100%;
      height: auto;
      border-radius: 12px;
      margin: calc($spacing-unit * 2.5) 0;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    }

    ul, ol {
      margin: calc($spacing-unit * 2) 0;
      padding-left: calc($spacing-unit * 2.5);
    }

    li {
      margin-bottom: calc($spacing-unit * 0.75);
      line-height: 1.6;
    }

    blockquote {
      border-left: 4px solid $brand-teal;
      padding-left: calc($spacing-unit * 2);
      margin: calc($spacing-unit * 2.5) 0;
      font-style: italic;
      color: rgba($muted-color, 0.9);
      background: rgba($brand-teal, 0.05);
      padding: calc($spacing-unit * 1.5) calc($spacing-unit * 2);
      border-radius: 0 8px 8px 0;
    }

    code {
      background: rgba($dark-background-color, 0.6);
      padding: 4px 8px;
      border-radius: 6px;
      font-family: 'Fira Code', 'Monaco', monospace;
      font-size: 0.9em;
      color: $brand-teal;
    }

    pre {
      background: rgba($dark-background-color, 0.6);
      padding: calc($spacing-unit * 2);
      border-radius: 12px;
      overflow-x: auto;
      margin: calc($spacing-unit * 2.5) 0;
      border: 1px solid rgba($border-color, 0.2);

      code {
        background: none;
        padding: 0;
        color: $muted-color;
      }
    }
  }
}

// Footer
.news-modal-footer {
  padding: calc($spacing-unit * 2.5) calc($spacing-unit * 3);
  border-top: 1px solid rgba($border-color, 0.1);
  background: rgba($background-color, 0.8);
  backdrop-filter: blur(10px);

  &-content {
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: calc($spacing-unit * 2);
    flex-wrap: wrap;
  }

  &-actions {
    display: flex;
    align-items: center;
    gap: calc($spacing-unit);
  }
}

.news-modal-tags {
  display: flex;
  align-items: center;
  gap: calc($spacing-unit * 0.75);
}

.news-modal-tag {
  background: rgba($brand-teal, 0.1);
  color: $brand-teal;
  padding: calc($spacing-unit * 0.5) calc($spacing-unit);
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid rgba($brand-teal, 0.2);

  &-event {
    background: rgba(#ff6b35, 0.1);
    color: #ff6b35;
    border-color: rgba(#ff6b35, 0.2);
  }
}

.news-modal-primary-button {
  display: flex;
  align-items: center;
  gap: calc($spacing-unit);
  background: linear-gradient(135deg, $brand-teal, #139b82);
  color: white;
  border: none;
  padding: calc($spacing-unit * 1.25) calc($spacing-unit * 2.5);
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.9rem;
  line-height: 1.3;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  box-shadow: 0 4px 16px rgba($brand-teal, 0.3);
  white-space: nowrap;
  min-height: 44px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba($brand-teal, 0.4);
  }

  &:active {
    transform: translateY(0);
  }

  svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  span {
    flex-shrink: 0;
  }
}

// Animations
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(60px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// YouTube embed styles
.youtube-embed-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; // 16:9 aspect ratio
  margin: calc($spacing-unit * 3) 0;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);

  iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 16px;
  }
}

.news-content .youtube-embed-container {
  margin: calc($spacing-unit * 3) 0;

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

// Responsive design
@media (max-width: 768px) {
  .news-modal-overlay {
    padding: calc($spacing-unit * 0.75);
  }

  .news-modal {
    max-height: 95vh;
    border-radius: 16px;
  }

  .news-modal-close-floating {
    top: calc($spacing-unit * 1.5);
    right: calc($spacing-unit * 1.5);
    width: 44px;
    height: 44px;
  }

  .news-modal-hero {
    height: 300px;

    &-overlay {
      padding: calc($spacing-unit * 3) calc($spacing-unit * 2) calc($spacing-unit * 2.5);
    }

    &-meta {
      gap: calc($spacing-unit * 1.5);
      margin-top: calc($spacing-unit * 1.5);

      .news-modal-meta-item {
        font-size: 0.85rem;

        svg {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  .news-modal-title {
    font-size: 2rem;
    line-height: 1.15;
  }

  .news-modal-article-meta {
    padding: calc($spacing-unit * 2);
    flex-direction: column;
    align-items: stretch;
    gap: calc($spacing-unit * 1.5);
  }

  .news-modal-meta-left {
    gap: calc($spacing-unit * 1.5);
  }

  .news-modal-meta-actions {
    align-self: flex-end;
  }

  .news-modal-article {
    padding: calc($spacing-unit * 2);
  }

  .news-modal-footer {
    padding: calc($spacing-unit * 2);

    &-content {
      flex-direction: column;
      align-items: stretch;
      gap: calc($spacing-unit * 1.5);
    }
  }

  .news-modal-primary-button {
    justify-content: center;
  }

  .news-modal-share-menu {
    right: auto;
    left: 0;
  }
}

@media (max-width: 480px) {
  .news-modal-overlay {
    padding: calc($spacing-unit * 0.5);
  }

  .news-modal {
    max-height: 98vh;
  }

  .news-modal-hero {
    height: 250px;

    &-overlay {
      padding: calc($spacing-unit * 2.5) calc($spacing-unit * 1.5) calc($spacing-unit * 2);
    }

    &-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: calc($spacing-unit);
      margin-top: calc($spacing-unit);

      .news-modal-meta-item {
        font-size: 0.8rem;

        svg {
          width: 14px;
          height: 14px;
        }
      }
    }
  }

  .news-modal-title {
    font-size: 1.75rem;
    line-height: 1.2;
  }

  .news-modal-meta-left {
    flex-direction: column;
    align-items: flex-start;
    gap: calc($spacing-unit);
  }

  .news-modal-article {
    padding: calc($spacing-unit * 1.5);
  }

  .news-modal-footer {
    padding: calc($spacing-unit * 1.5);
  }
}


