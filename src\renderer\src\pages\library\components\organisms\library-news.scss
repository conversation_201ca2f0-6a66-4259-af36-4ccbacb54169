@use "../../../../scss/globals.scss";

.library-news {
  margin-bottom: calc(globals.$spacing-unit * 3);

  &__error {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: calc(globals.$spacing-unit * 2);
    background: rgba(#ff6b6b, 0.1);
    border: 1px solid rgba(#ff6b6b, 0.3);
    border-radius: 8px;
    color: #ff6b6b;
    text-align: center;

    p {
      margin: 0;
    }
  }

  &__placeholder {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);

    &-content {
      text-align: center;
      opacity: 0.7;

      h2 {
        margin: 0 0 calc(globals.$spacing-unit) 0;
        font-size: 1.5rem;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-bottom: calc(globals.$spacing-unit);
    min-height: 24px;
  }

  &__refresh-indicator {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit / 2);
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.6);
  }

  &__refresh-dot {
    width: 8px;
    height: 8px;
    background: #4CAF50;
    border-radius: 50%;
    animation: pulse 1.5s ease-in-out infinite;
  }

  &__refresh-text {
    font-size: 0.875rem;
  }

  &__refresh-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.8);
    padding: calc(globals.$spacing-unit / 2);
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      color: white;
    }

    &:active {
      transform: scale(0.95);
    }
  }

  &__retry-button {
    background: rgba(76, 175, 80, 0.2);
    border: 1px solid rgba(76, 175, 80, 0.4);
    border-radius: 6px;
    color: #4CAF50;
    padding: calc(globals.$spacing-unit / 2) calc(globals.$spacing-unit);
    cursor: pointer;
    font-size: 0.875rem;
    margin-top: calc(globals.$spacing-unit);
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      background: rgba(76, 175, 80, 0.3);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 1000px;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}
